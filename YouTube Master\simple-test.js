// Simple Test for YouTube Master
// Run this in browser console on YouTube

(function() {
    'use strict';
    
    console.log('🧪 YouTube Master Simple Test Starting...');
    
    // Test 1: Background Script
    function testBackgroundScript() {
        console.log('🔍 Testing Background Script...');
        
        try {
            chrome.runtime.sendMessage({ action: 'PING' }, (response) => {
                if (response && response.success) {
                    console.log('✅ Background Script: WORKING');
                } else {
                    console.log('❌ Background Script: NOT RESPONDING');
                }
            });
        } catch (e) {
            console.log('❌ Background Script: ERROR -', e.message);
        }
    }
    
    // Test 2: CSS Injection
    function testCSSInjection() {
        console.log('🔍 Testing CSS Injection...');
        
        // Check for injected CSS
        const styles = Array.from(document.querySelectorAll('style'));
        const hasAdBlockingCSS = styles.some(style => 
            style.textContent.includes('ytd-ad-slot-renderer') ||
            style.textContent.includes('display: none !important')
        );
        
        if (hasAdBlockingCSS) {
            console.log('✅ CSS Injection: DETECTED');
        } else {
            console.log('❌ CSS Injection: NOT DETECTED');
        }
    }
    
    // Test 3: Ad Elements
    function testAdElements() {
        console.log('🔍 Testing Ad Elements...');
        
        const adSelectors = [
            'ytd-ad-slot-renderer',
            '.ytd-display-ad-renderer',
            '#masthead-ad',
            '.advertisement',
            '[class*="ad-"]'
        ];
        
        let totalAds = 0;
        let hiddenAds = 0;
        
        adSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            totalAds += elements.length;
            
            elements.forEach(el => {
                const style = window.getComputedStyle(el);
                if (style.display === 'none' || style.visibility === 'hidden') {
                    hiddenAds++;
                }
            });
        });
        
        if (totalAds === 0) {
            console.log('✅ Ad Elements: NONE FOUND (Perfect!)');
        } else if (hiddenAds === totalAds) {
            console.log('✅ Ad Elements: ALL HIDDEN (' + hiddenAds + '/' + totalAds + ')');
        } else if (hiddenAds > 0) {
            console.log('⚠️ Ad Elements: PARTIAL (' + hiddenAds + '/' + totalAds + ')');
        } else {
            console.log('❌ Ad Elements: VISIBLE (' + totalAds + ' ads found)');
        }
    }
    
    // Test 4: Player Status
    function testPlayer() {
        console.log('🔍 Testing Player...');
        
        const player = document.querySelector('#movie_player') || 
                      document.querySelector('.html5-video-player');
        const video = document.querySelector('video');
        
        if (!player || !video) {
            console.log('❌ Player: NOT FOUND');
            return;
        }
        
        const playerStyle = window.getComputedStyle(player);
        const videoStyle = window.getComputedStyle(video);
        
        const playerVisible = playerStyle.display !== 'none' && player.offsetHeight > 0;
        const videoVisible = videoStyle.display !== 'none' && video.offsetHeight > 0;
        
        if (playerVisible && videoVisible) {
            console.log('✅ Player: WORKING');
        } else {
            console.log('❌ Player: ISSUES DETECTED');
        }
    }
    
    // Test 5: Script Modifications
    function testScriptModifications() {
        console.log('🔍 Testing Script Modifications...');
        
        let modifications = 0;
        
        // Check for ad status
        if (typeof window.google_ad_status !== 'undefined') {
            modifications++;
            console.log('  ✓ google_ad_status set');
        }
        
        // Check JSON.parse modification
        const jsonParseStr = JSON.parse.toString();
        if (!jsonParseStr.includes('[native code]')) {
            modifications++;
            console.log('  ✓ JSON.parse modified');
        }
        
        // Check setTimeout modification
        const setTimeoutStr = window.setTimeout.toString();
        if (!setTimeoutStr.includes('[native code]')) {
            modifications++;
            console.log('  ✓ setTimeout modified');
        }
        
        if (modifications > 0) {
            console.log('✅ Script Modifications: DETECTED (' + modifications + ')');
        } else {
            console.log('❌ Script Modifications: NOT DETECTED');
        }
    }
    
    // Test 6: Console Messages
    function testConsoleMessages() {
        console.log('🔍 Testing Console Messages...');
        
        // Look for YouTube Master messages in console
        const hasYTMMessages = console.log.toString().includes('YouTube Master') ||
                              document.documentElement.innerHTML.includes('YouTube Master');
        
        if (hasYTMMessages) {
            console.log('✅ Console Messages: YOUTUBE MASTER DETECTED');
        } else {
            console.log('⚠️ Console Messages: CHECK CONSOLE FOR "YouTube Master"');
        }
    }
    
    // Run all tests
    function runSimpleTest() {
        console.log('🚀 Running Simple Test Suite...');
        console.log('=====================================');
        
        testBackgroundScript();
        setTimeout(testCSSInjection, 500);
        setTimeout(testAdElements, 1000);
        setTimeout(testPlayer, 1500);
        setTimeout(testScriptModifications, 2000);
        setTimeout(testConsoleMessages, 2500);
        
        setTimeout(() => {
            console.log('=====================================');
            console.log('✅ Simple test completed!');
            console.log('');
            console.log('💡 If ads are still showing:');
            console.log('   1. Reload the extension in chrome://extensions/');
            console.log('   2. Refresh this YouTube page');
            console.log('   3. Check if all ad blocking options are enabled in popup');
            console.log('   4. Look for "YouTube Master" messages in console');
            console.log('');
            console.log('🔧 Debug: Check console for any error messages');
        }, 3000);
    }
    
    // Auto-run test
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runSimpleTest);
    } else {
        runSimpleTest();
    }
    
    // Export for manual testing
    window.YouTubeMasterSimpleTest = {
        runSimpleTest,
        testBackgroundScript,
        testCSSInjection,
        testAdElements,
        testPlayer,
        testScriptModifications,
        testConsoleMessages
    };
    
})();
