// YouTube Master - Simple and Working Ad Blocker
console.log('YouTube Master: Background script starting...');

// Settings
let settings = {
  adBlocking: true,
  networkBlocking: true,
  cssBlocking: true,
  scriptletBlocking: true
};

// Network blocking rules (simple and effective)
const networkRules = [
  {
    "id": 1,
    "priority": 1,
    "action": {"type": "block"},
    "condition": {
      "urlFilter": "*://youtube.com/pagead/*",
      "resourceTypes": ["xmlhttprequest", "script", "image"]
    }
  },
  {
    "id": 2,
    "priority": 1,
    "action": {"type": "block"},
    "condition": {
      "urlFilter": "*://googlesyndication.com/*",
      "resourceTypes": ["xmlhttprequest", "script", "image"]
    }
  },
  {
    "id": 3,
    "priority": 1,
    "action": {"type": "block"},
    "condition": {
      "urlFilter": "*://googleads.g.doubleclick.net/*",
      "resourceTypes": ["xmlhttprequest", "script", "image"]
    }
  },
  {
    "id": 4,
    "priority": 1,
    "action": {"type": "block"},
    "condition": {
      "urlFilter": "*://doubleclick.com/*",
      "resourceTypes": ["xmlhttprequest", "script", "image"]
    }
  },
  {
    "id": 5,
    "priority": 1,
    "action": {"type": "block"},
    "condition": {
      "urlFilter": "*://youtube.com/youtubei/v1/player/ad_break*",
      "resourceTypes": ["xmlhttprequest"]
    }
  }
];

// CSS rules (comprehensive ad blocking)
const cssRules = `
/* Hide all ad containers */
ytd-ad-slot-renderer,
.ytd-display-ad-renderer,
.ytd-promoted-sparkles-web-renderer,
.ytd-video-masthead-ad-v3-renderer,
.ytd-primetime-promo-renderer,
#masthead-ad,
#offer-module,
#promotion-shelf,
.ad-showing,
.ads-pool,
.advertisement,
[class*="ad-"],
[id*="ad-"],
[data-ad-slot],
.ytp-suggested-action,
.ytp-ce-element-show,
.ytp-cards-teaser,
.ytp-ce-covering-overlay,
ytd-rich-item-renderer:has(ytd-ad-slot-renderer),
.ytd-watch-next-secondary-results-renderer ytd-ad-slot-renderer,
.ytd-item-section-renderer ytd-ad-slot-renderer {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
}
`;

// Scriptlets (ad blocking JavaScript)
const scriptlets = `
(function() {
  'use strict';
  console.log('YouTube Master: Scriptlets loading...');
  
  try {
    // Block ad status
    window.google_ad_status = 1;
    
    // Intercept JSON responses
    const originalParse = JSON.parse;
    JSON.parse = function(text) {
      try {
        const result = originalParse.call(this, text);
        if (result && typeof result === 'object') {
          // Remove ad properties
          if (result.adPlacements) delete result.adPlacements;
          if (result.adSlots) delete result.adSlots;
          if (result.playerAds) delete result.playerAds;
          
          if (result.playerResponse) {
            if (result.playerResponse.adPlacements) delete result.playerResponse.adPlacements;
            if (result.playerResponse.adSlots) delete result.playerResponse.adSlots;
            if (result.playerResponse.playerAds) delete result.playerResponse.playerAds;
          }
        }
        return result;
      } catch (e) {
        return originalParse.call(this, text);
      }
    };
    
    // Speed up ad timeouts
    const originalSetTimeout = window.setTimeout;
    window.setTimeout = function(callback, delay, ...args) {
      if (delay === 17000 || delay === 15000 || delay === 10000) {
        delay = 1;
      }
      return originalSetTimeout.call(this, callback, delay, ...args);
    };
    
    console.log('YouTube Master: Scriptlets loaded successfully');
  } catch (e) {
    console.error('YouTube Master: Scriptlet error:', e);
  }
})();
`;

// Setup network rules
async function setupNetworkRules() {
  try {
    console.log('YouTube Master: Setting up network rules...');
    
    // Clear existing rules
    const existingRules = await chrome.declarativeNetRequest.getDynamicRules();
    const removeRuleIds = existingRules.map(r => r.id);
    
    if (removeRuleIds.length > 0) {
      await chrome.declarativeNetRequest.updateDynamicRules({
        removeRuleIds: removeRuleIds
      });
    }
    
    // Add new rules
    await chrome.declarativeNetRequest.updateDynamicRules({
      addRules: networkRules
    });
    
    console.log('YouTube Master: Network rules added successfully:', networkRules.length);
  } catch (error) {
    console.error('YouTube Master: Error setting up network rules:', error);
  }
}

// Inject CSS
async function injectCSS(tabId) {
  try {
    if (!settings.cssBlocking) return;
    
    console.log('YouTube Master: Injecting CSS for tab:', tabId);
    
    await chrome.scripting.insertCSS({
      css: cssRules,
      origin: 'USER',
      target: { tabId: tabId }
    });
    
    console.log('YouTube Master: CSS injected successfully');
  } catch (error) {
    console.error('YouTube Master: Error injecting CSS:', error);
  }
}

// Inject scriptlets
async function injectScriptlets(tabId) {
  try {
    if (!settings.scriptletBlocking) return;
    
    console.log('YouTube Master: Injecting scriptlets for tab:', tabId);
    
    await chrome.scripting.executeScript({
      target: { tabId: tabId },
      func: function(scriptContent) {
        try {
          const script = document.createElement('script');
          script.textContent = scriptContent;
          (document.head || document.documentElement).appendChild(script);
          script.remove();
        } catch (e) {
          console.error('Script injection error:', e);
        }
      },
      args: [scriptlets],
      injectImmediately: true,
      world: 'MAIN'
    });
    
    console.log('YouTube Master: Scriptlets injected successfully');
  } catch (error) {
    console.error('YouTube Master: Error injecting scriptlets:', error);
  }
}

// Complete injection
async function performCompleteInjection(tabId) {
  try {
    if (!settings.adBlocking) {
      console.log('YouTube Master: Ad blocking disabled');
      return;
    }
    
    console.log('YouTube Master: Performing complete injection for tab:', tabId);
    
    await Promise.all([
      injectCSS(tabId),
      injectScriptlets(tabId)
    ]);
    
    console.log('YouTube Master: Complete injection finished');
  } catch (error) {
    console.error('YouTube Master: Error in complete injection:', error);
  }
}

// Check if URL is YouTube
function isYouTubeURL(url) {
  return url && /youtube\.com/.test(url);
}

// Tab monitoring
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'loading' && isYouTubeURL(tab.url)) {
    console.log('YouTube Master: YouTube tab loading, injecting...', tab.url);
    setTimeout(() => performCompleteInjection(tabId), 100);
  }
});

// Navigation monitoring
chrome.webNavigation.onHistoryStateUpdated.addListener(async (details) => {
  if (isYouTubeURL(details.url)) {
    console.log('YouTube Master: YouTube navigation detected, re-injecting...', details.url);
    setTimeout(() => performCompleteInjection(details.tabId), 500);
  }
});

// Page completion
chrome.webNavigation.onCompleted.addListener(async (details) => {
  if (details.frameId === 0 && isYouTubeURL(details.url)) {
    console.log('YouTube Master: YouTube page completed, final injection...', details.url);
    setTimeout(() => performCompleteInjection(details.tabId), 1000);
  }
});

// Message handling
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('YouTube Master: Received message:', request);
  
  if (request.action === 'getSettings') {
    sendResponse({ settings });
  } else if (request.action === 'updateSettings') {
    Object.assign(settings, request.settings);
    chrome.storage.local.set(settings);
    setupNetworkRules();
    sendResponse({ success: true });
  } else if (request.action === 'PAGE_READY') {
    sendResponse({ 
      ads: settings.adBlocking,
      popupConfig: { isAntiAdblockPopupEnabled: false }
    });
  } else if (request.action === 'PING') {
    sendResponse({ success: true });
  }
  
  return true;
});

// Installation
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('YouTube Master: Extension installed/updated');
  
  if (details.reason === "install") {
    chrome.tabs.create({ url: 'https://www.emiratalyoum.com/34422/' });
  }
  
  // Load settings
  const result = await chrome.storage.local.get(settings);
  settings = { ...settings, ...result };
  
  // Setup network rules
  await setupNetworkRules();
  
  console.log('YouTube Master: Initialization complete');
});

chrome.runtime.setUninstallURL('https://y-t-master.blogspot.com/');

// Startup
(async () => {
  console.log('YouTube Master: Starting up...');
  
  // Load settings
  const result = await chrome.storage.local.get(settings);
  settings = { ...settings, ...result };
  
  // Setup network rules
  await setupNetworkRules();
  
  console.log('YouTube Master: Ready!');
})();
