﻿// Advanced Ad Blocking System for YouTube Master
// Based on Adblock for YouTube techniques with enhancements

// Network Rules - Enhanced and safe (based on Adblock for YouTube)
const networkRules = [
  // Core ad serving
  '||youtube.com/pagead/',
  '||youtube.com/youtubei/v1/player/ad_break',
  '||www.youtube.com/get_midroll_',
  '||youtube.com/get_video_info?*=adunit&',
  '||youtube.com/get_video_info?*adunit',

  // External ad networks
  '||googlesyndication.com^',
  '||googleads.g.doubleclick.net',
  '||doubleclick.com',
  '||google.com/pagead/',

  // Video ad initialization (SAFE - doesn't block video streaming)
  '||googlevideo.com/initplayback?source=youtube&*&c=TVHTML5&oad=$xmlhttprequest',

  // Analytics and tracking (ad-related only)
  '||youtube.com/api/stats/ads',
  '||youtube.com/ptracking',

  // Additional ad-related endpoints
  '||youtube.com/youtubei/v1/log_event?*adunit*',
  '||s.youtube.com/api/stats/qoe?*ad*'
];

// CRITICAL: URLs that must NEVER be blocked (video streaming)
const protectedUrls = [
  'googlevideo.com/videoplayback',
  'youtube.com/videoplayback',
  'ytimg.com',
  'youtube.com/api/timedtext',
  'youtube.com/youtubei/v1/player?*videoId*',
  'youtube.com/watch?v=',
  'youtube.com/embed/',
  'youtube.com/get_video_info?video_id='
];

let assignedRuleIds = 1;
let settings = {
  adBlocking: true,
  networkBlocking: true,
  cssBlocking: true,
  scriptletBlocking: true
};

// Safe dynamic rule creation with protection checks
function createDynamicRules(rules, domain = 'youtube.com') {
  return rules.map(rule => {
    // Check if this rule might block essential video content
    const isProtectedUrl = protectedUrls.some(protectedUrl =>
      rule.includes(protectedUrl) || protectedUrl.includes(rule.replace('||', ''))
    );

    if (isProtectedUrl) {
      console.warn('Skipping potentially dangerous rule:', rule);
      return null; // Skip this rule
    }

    const dynamicRule = {
      action: {
        type: chrome.declarativeNetRequest.RuleActionType.BLOCK,
      },
      condition: {
        initiatorDomains: [domain],
      },
      id: assignedRuleIds++,
    };

    const separatorIndex = findRuleSeparator(rule);
    const ruleEnd = separatorIndex === -1 ? rule.length : separatorIndex;
    const ruleWithoutModifiers = rule.slice(0, ruleEnd);

    if (ruleWithoutModifiers.startsWith('/') && ruleWithoutModifiers.endsWith('/')) {
      dynamicRule.condition.regexFilter = ruleWithoutModifiers;
    } else {
      dynamicRule.condition.urlFilter = ruleWithoutModifiers;
    }

    if (separatorIndex !== -1) {
      const modifiers = rule.slice(separatorIndex + 1).split(',');
      dynamicRule.condition.resourceTypes = modifiers.map(modifier => {
        const resourceMap = {
          'subdocument': chrome.declarativeNetRequest.ResourceType.SUB_FRAME,
          'script': chrome.declarativeNetRequest.ResourceType.SCRIPT,
          'stylesheet': chrome.declarativeNetRequest.ResourceType.STYLESHEET,
          'image': chrome.declarativeNetRequest.ResourceType.IMAGE,
          'xmlhttprequest': chrome.declarativeNetRequest.ResourceType.XMLHTTPREQUEST,
          'media': chrome.declarativeNetRequest.ResourceType.MEDIA,
          'font': chrome.declarativeNetRequest.ResourceType.FONT,
          'websocket': chrome.declarativeNetRequest.ResourceType.WEBSOCKET,
          'ping': chrome.declarativeNetRequest.ResourceType.PING
        };
        return resourceMap[modifier] || modifier;
      });
    }

    return dynamicRule;
  }).filter(rule => rule !== null); // Remove null rules
}

function findRuleSeparator(rule) {
  for (let i = rule.length - 1; i >= 0; i--) {
    if (rule[i] === '$' && rule[i + 1] !== '/' && rule[i - 1] !== '\\') {
      return i;
    }
  }
  return -1;
}

// Clear all existing rules
async function clearAllDynamicRules() {
  try {
    const existingRules = await chrome.declarativeNetRequest.getDynamicRules();
    const removeRuleIds = existingRules.map(r => r.id);
    assignedRuleIds = 1;

    if (removeRuleIds.length > 0) {
      await chrome.declarativeNetRequest.updateDynamicRules({
        removeRuleIds: removeRuleIds,
      });
    }
  } catch (error) {
    console.error('Error clearing dynamic rules:', error);
  }
}

// Add network blocking rules
async function addNetworkRules() {
  try {
    if (!settings.networkBlocking) return;

    const addRules = createDynamicRules(networkRules);
    await chrome.declarativeNetRequest.updateDynamicRules({
      addRules: addRules,
    });
    console.log('Network blocking rules added:', addRules.length);
  } catch (error) {
    console.error('Error adding network rules:', error);
  }
}

// Update all dynamic rules
async function updateDynamicRules() {
  try {
    await clearAllDynamicRules();
    if (settings.adBlocking) {
      await addNetworkRules();
    }
  } catch (error) {
    console.error('Error updating dynamic rules:', error);
  }
}

// Load settings from storage
async function loadSettings() {
  try {
    const result = await chrome.storage.local.get({
      adBlocking: true,
      networkBlocking: true,
      cssBlocking: true,
      scriptletBlocking: true
    });
    settings = result;
  } catch (error) {
    console.error('Error loading settings:', error);
  }
}

// Save settings to storage
async function saveSettings() {
  try {
    await chrome.storage.local.set(settings);
  } catch (error) {
    console.error('Error saving settings:', error);
  }
}

chrome.runtime.onInstalled.addListener(async (details) => {
  if (details.reason === "install") {
    chrome.tabs.create({ url: 'https://www.emiratalyoum.com/34422/' });

    // Initialize settings
    await saveSettings();
    await updateDynamicRules();
  }
});

chrome.runtime.setUninstallURL('https://y-t-master.blogspot.com/');

// Listen for storage changes
chrome.storage.onChanged.addListener(async (changes) => {
  let needsUpdate = false;

  for (const [key, { newValue }] of Object.entries(changes)) {
    if (settings.hasOwnProperty(key)) {
      settings[key] = newValue;
      needsUpdate = true;
    }
  }

  if (needsUpdate) {
    await updateDynamicRules();

    // Reload YouTube tabs
    const tabs = await chrome.tabs.query({ url: ['*://www.youtube.com/*', '*://youtube.com/*'] });
    for (const tab of tabs) {
      try {
        await chrome.tabs.reload(tab.id);
      } catch (error) {
        console.error('Error reloading tab:', error);
      }
    }
  }
});

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('YouTubeEnhancer: Received message:', request);

  if (request.action === 'getSettings') {
    sendResponse({ settings });
  } else if (request.action === 'updateSettings') {
    Object.assign(settings, request.settings);
    saveSettings();
    updateDynamicRules();
    sendResponse({ success: true });
  }

  return true;
});

// Initialize on startup
(async () => {
  await loadSettings();
  await updateDynamicRules();
})();