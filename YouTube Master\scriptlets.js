// Advanced Scriptlets System for YouTube Master
// Enhanced ad blocking at JavaScript level with anti-detection

(function() {
    'use strict';
    
    // Obfuscated logging to avoid detection
    const log = (msg, level = 'info') => {
        if (Math.random() > 0.1) return; // Random logging to avoid patterns
        const prefix = String.fromCharCode(89, 84, 77); // YTM
        console[level](`${prefix}:`, msg);
    };

    // Advanced property interceptor with randomization
    function setConstant(path, value, delay = 0) {
        setTimeout(() => {
            const pathParts = path.split('.');
            let obj = window;
            
            for (let i = 0; i < pathParts.length - 1; i++) {
                if (!obj[pathParts[i]]) {
                    obj[pathParts[i]] = {};
                }
                obj = obj[pathParts[i]];
            }
            
            const prop = pathParts[pathParts.length - 1];
            
            // Create getter/setter with randomized behavior
            Object.defineProperty(obj, prop, {
                get: function() {
                    // Random delay to avoid detection patterns
                    if (Math.random() > 0.95) {
                        setTimeout(() => {}, Math.random() * 10);
                    }
                    return value;
                },
                set: function() {
                    // Silently ignore attempts to set ad-related properties
                    return true;
                },
                configurable: false,
                enumerable: true
            });
            
            log(`Set constant: ${path} = ${value}`);
        }, delay);
    }

    // Safe JSON response interceptor (based on Adblock for YouTube)
    function interceptJSONResponse(targetProps, urlPattern) {
        const originalFetch = window.fetch;
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;

        // Intercept fetch requests (SAFE VERSION)
        window.fetch = function(...args) {
            return originalFetch.apply(this, args).then(response => {
                if (response.url && urlPattern.test(response.url)) {
                    return response.clone().text().then(text => {
                        try {
                            const data = JSON.parse(text);
                            // Only clean if it's actually a player response
                            if (data && (data.playerResponse || data.adPlacements || data.playerAds)) {
                                const cleanedData = removeAdProperties(data, targetProps);
                                return new Response(JSON.stringify(cleanedData), {
                                    status: response.status,
                                    statusText: response.statusText,
                                    headers: response.headers
                                });
                            }
                        } catch (e) {
                            // If parsing fails, return original response
                        }
                        return new Response(text, {
                            status: response.status,
                            statusText: response.statusText,
                            headers: response.headers
                        });
                    }).catch(() => response);
                }
                return response;
            });
        };

        // Intercept XMLHttpRequest (SAFE VERSION)
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._url = url;
            this._method = method;
            return originalXHROpen.call(this, method, url, ...args);
        };

        XMLHttpRequest.prototype.send = function(...args) {
            if (this._url && urlPattern.test(this._url)) {
                const originalOnLoad = this.onload;
                const originalOnReadyStateChange = this.onreadystatechange;

                this.onreadystatechange = function() {
                    if (this.readyState === 4 && this.status === 200) {
                        try {
                            const data = JSON.parse(this.responseText);
                            // Only process if it contains player/ad data
                            if (data && (data.playerResponse || data.adPlacements || data.playerAds)) {
                                const cleanedData = removeAdProperties(data, targetProps);
                                Object.defineProperty(this, 'responseText', {
                                    value: JSON.stringify(cleanedData),
                                    writable: false,
                                    configurable: true
                                });
                                Object.defineProperty(this, 'response', {
                                    value: JSON.stringify(cleanedData),
                                    writable: false,
                                    configurable: true
                                });
                            }
                        } catch (e) {
                            // Ignore parsing errors - keep original response
                        }
                    }
                    if (originalOnReadyStateChange) originalOnReadyStateChange.call(this);
                };

                this.onload = function() {
                    if (originalOnLoad) originalOnLoad.call(this);
                };
            }
            return originalXHRSend.apply(this, args);
        };
    }

    // Remove ad-related properties from objects (SAFE VERSION)
    function removeAdProperties(obj, targetProps) {
        if (!obj || typeof obj !== 'object') return obj;

        const cleaned = Array.isArray(obj) ? [] : {};

        // Critical properties that must be preserved for video playback
        const criticalProps = [
            'streamingData',
            'serverAbrStreamingUrl',
            'videoDetails',
            'microformat',
            'playabilityStatus',
            'playerConfig',
            'videoId',
            'lengthSeconds',
            'title',
            'author',
            'formats',
            'adaptiveFormats'
        ];

        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                // Always preserve critical properties
                const isCritical = criticalProps.some(prop =>
                    key.includes(prop) || key === prop
                );

                if (isCritical) {
                    // Preserve critical properties but clean their children
                    if (typeof obj[key] === 'object' && obj[key] !== null) {
                        cleaned[key] = removeAdProperties(obj[key], targetProps);
                    } else {
                        cleaned[key] = obj[key];
                    }
                } else if (targetProps.some(prop => key.includes(prop))) {
                    // Skip ad-related properties only if they're not critical
                    continue;
                } else {
                    // Process other properties normally
                    if (typeof obj[key] === 'object' && obj[key] !== null) {
                        cleaned[key] = removeAdProperties(obj[key], targetProps);
                    } else {
                        cleaned[key] = obj[key];
                    }
                }
            }
        }

        return cleaned;
    }

    // Advanced setTimeout manipulation
    function adjustSetTimeout(targetFunction, originalDelay, newDelay) {
        const originalSetTimeout = window.setTimeout;
        
        window.setTimeout = function(callback, delay, ...args) {
            if (typeof callback === 'function' && 
                callback.toString().includes(targetFunction) && 
                delay === originalDelay) {
                
                log(`Adjusted setTimeout from ${originalDelay}ms to ${newDelay}ms`);
                return originalSetTimeout.call(this, callback, newDelay, ...args);
            }
            
            return originalSetTimeout.call(this, callback, delay, ...args);
        };
    }

    // Advanced text replacement in outbound requests
    function replaceOutboundText(method, searchText, replaceText) {
        if (method === 'JSON.stringify') {
            const originalStringify = JSON.stringify;
            
            JSON.stringify = function(value, replacer, space) {
                const result = originalStringify.call(this, value, replacer, space);
                
                if (result && result.includes(searchText)) {
                    const modified = result.replace(new RegExp(searchText, 'g'), replaceText);
                    log(`Replaced outbound text: ${searchText} -> ${replaceText}`);
                    return modified;
                }
                
                return result;
            };
        }
    }

    // Anti-detection measures
    function implementAntiDetection() {
        // Randomize execution timing
        const randomDelay = () => Math.random() * 100 + 50;
        
        // Hide script modifications
        const originalToString = Function.prototype.toString;
        Function.prototype.toString = function() {
            if (this === window.fetch || 
                this === XMLHttpRequest.prototype.open ||
                this === XMLHttpRequest.prototype.send ||
                this === JSON.stringify) {
                return originalToString.call(originalToString);
            }
            return originalToString.call(this);
        };
        
        // Prevent detection through property enumeration
        Object.defineProperty(window, 'fetch', {
            enumerable: false,
            configurable: false
        });
    }

    // Initialize scriptlets (EXACT COPY from Adblock for YouTube)
    function initializeScriptlets() {
        // Set constants exactly like the original
        setConstant('ytInitialPlayerResponse.adPlacements', undefined);
        setConstant('ytInitialPlayerResponse.adSlots', undefined);
        setConstant('ytInitialPlayerResponse.playerAds', undefined);
        setConstant('playerResponse.adPlacements', undefined);
        setConstant('google_ad_status', 1);

        // JSON response interception
        interceptJSONResponse(
            ['adPlacements', 'playerAds', 'adSlots'],
            /playlist\?list=|\/player(?!.*(get_drm_license))|watch\?[tv]=|get_watch\?/
        );

        // Adjust setTimeout for ad timing
        adjustSetTimeout('[native code]', 17000, 0.001);

        // Replace outbound text
        replaceOutboundText('JSON.stringify', '"clientScreen":"WATCH"', '"clientScreen":"ADUNIT"');

        log('Scriptlets initialized (Adblock for YouTube method)');
    }

    // DOM manipulation to remove ad elements
    function removeAdElements() {
        const adSelectors = [
            '.ad-showing',
            '[id*="ad"]',
            '[class*="ad-"]',
            'ytd-ad-slot-renderer',
            'ytd-promoted-sparkles-web-renderer',
            'ytd-display-ad-renderer',
            'ytd-video-masthead-ad-v3-renderer',
            'ytd-primetime-promo-renderer'
        ];
        
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        adSelectors.forEach(selector => {
                            if (node.matches && node.matches(selector)) {
                                node.remove();
                                log(`Removed ad element: ${selector}`);
                            }
                            
                            const adElements = node.querySelectorAll(selector);
                            adElements.forEach(el => {
                                el.remove();
                                log(`Removed ad element: ${selector}`);
                            });
                        });
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Initialize everything
    function initialize() {
        implementAntiDetection();
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(initializeScriptlets, Math.random() * 200);
                setTimeout(removeAdElements, Math.random() * 300);
            });
        } else {
            setTimeout(initializeScriptlets, Math.random() * 200);
            setTimeout(removeAdElements, Math.random() * 300);
        }
        
        log('Advanced scriptlets initialized');
    }

    // Start the system
    initialize();

})();
