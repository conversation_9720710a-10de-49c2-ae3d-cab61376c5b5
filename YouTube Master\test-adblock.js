// Advanced Ad Blocking Test Suite for YouTube Master
// This script tests all ad blocking functionalities

(function() {
    'use strict';
    
    console.log('🧪 Starting YouTube Master Ad Blocking Test Suite...');
    
    // Test Results Storage
    const testResults = {
        networkBlocking: false,
        cssBlocking: false,
        scriptletBlocking: false,
        antiDetection: false,
        smartMonitoring: false
    };
    
    // Test 1: Network Blocking
    function testNetworkBlocking() {
        console.log('🔍 Testing Network Blocking...');
        
        // Test if declarativeNetRequest rules are active
        chrome.declarativeNetRequest.getDynamicRules().then(rules => {
            const adBlockingRules = rules.filter(rule => 
                rule.condition.urlFilter && 
                (rule.condition.urlFilter.includes('googlesyndication') ||
                 rule.condition.urlFilter.includes('doubleclick') ||
                 rule.condition.urlFilter.includes('pagead'))
            );
            
            if (adBlockingRules.length > 0) {
                testResults.networkBlocking = true;
                console.log('✅ Network Blocking: ACTIVE (' + adBlockingRules.length + ' rules)');
            } else {
                console.log('❌ Network Blocking: INACTIVE');
            }
        }).catch(error => {
            console.log('❌ Network Blocking: ERROR -', error);
        });
    }
    
    // Test 2: CSS Blocking
    function testCSSBlocking() {
        console.log('🔍 Testing CSS Blocking...');
        
        const testSelectors = [
            'ytd-ad-slot-renderer',
            '.ytd-display-ad-renderer',
            '#masthead-ad',
            '.ytd-promoted-sparkles-web-renderer'
        ];
        
        let hiddenElements = 0;
        testSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                const style = window.getComputedStyle(el);
                if (style.display === 'none') {
                    hiddenElements++;
                }
            });
        });
        
        if (hiddenElements > 0 || document.querySelectorAll('style[data-yt-]').length > 0) {
            testResults.cssBlocking = true;
            console.log('✅ CSS Blocking: ACTIVE (' + hiddenElements + ' elements hidden)');
        } else {
            console.log('❌ CSS Blocking: INACTIVE');
        }
    }
    
    // Test 3: Scriptlet Blocking
    function testScriptletBlocking() {
        console.log('🔍 Testing Scriptlet Blocking...');
        
        // Check if scriptlets have modified global objects
        const tests = [
            () => typeof window.ytInitialPlayerResponse !== 'undefined',
            () => window.fetch !== window.fetch.toString().includes('[native code]'),
            () => JSON.stringify !== JSON.stringify.toString().includes('[native code]'),
            () => XMLHttpRequest.prototype.open !== XMLHttpRequest.prototype.open.toString().includes('[native code]')
        ];
        
        const activeScriptlets = tests.filter(test => {
            try {
                return test();
            } catch (e) {
                return false;
            }
        }).length;
        
        if (activeScriptlets > 0) {
            testResults.scriptletBlocking = true;
            console.log('✅ Scriptlet Blocking: ACTIVE (' + activeScriptlets + ' scriptlets detected)');
        } else {
            console.log('❌ Scriptlet Blocking: INACTIVE');
        }
    }
    
    // Test 4: Anti-Detection
    function testAntiDetection() {
        console.log('🔍 Testing Anti-Detection...');
        
        const detectionTests = [
            // Check if Function.prototype.toString is modified
            () => Function.prototype.toString.toString().includes('[native code]'),
            // Check if navigator.webdriver is hidden
            () => navigator.webdriver === undefined,
            // Check if setTimeout has jitter
            () => {
                const start = Date.now();
                setTimeout(() => {}, 100);
                const end = Date.now();
                return Math.abs(end - start - 100) > 5; // Jitter detected
            }
        ];
        
        const antiDetectionFeatures = detectionTests.filter(test => {
            try {
                return test();
            } catch (e) {
                return false;
            }
        }).length;
        
        if (antiDetectionFeatures > 0) {
            testResults.antiDetection = true;
            console.log('✅ Anti-Detection: ACTIVE (' + antiDetectionFeatures + ' features)');
        } else {
            console.log('❌ Anti-Detection: INACTIVE');
        }
    }
    
    // Test 5: Smart Monitoring
    function testSmartMonitoring() {
        console.log('🔍 Testing Smart Monitoring...');
        
        // Check for detection elements
        const detectionSelectors = [
            'ytd-enforcement-message-view-model[in-player]',
            '[data-testid="ad-blocker-detection"]',
            '.ytd-popup-container:has([aria-label*="ad blocker"])'
        ];
        
        let detectionElements = 0;
        detectionSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                detectionElements += elements.length;
            } catch (e) {
                // Ignore invalid selectors
            }
        });
        
        // Check if monitoring is active (localStorage flags)
        const monitoringFlags = [
            localStorage.getItem('lastAdBlockReload'),
            localStorage.getItem('adBlockReloadFlag')
        ].filter(flag => flag !== null).length;
        
        if (detectionElements === 0 && monitoringFlags > 0) {
            testResults.smartMonitoring = true;
            console.log('✅ Smart Monitoring: ACTIVE (No detection elements found)');
        } else if (detectionElements > 0) {
            console.log('⚠️ Smart Monitoring: DETECTION FOUND (' + detectionElements + ' elements)');
        } else {
            console.log('❌ Smart Monitoring: INACTIVE');
        }
    }
    
    // Test 6: Performance Impact
    function testPerformance() {
        console.log('🔍 Testing Performance Impact...');
        
        const startTime = performance.now();
        
        // Simulate typical YouTube operations
        const operations = [
            () => document.querySelectorAll('video').length,
            () => document.querySelectorAll('[class*="ad"]').length,
            () => window.fetch ? true : false,
            () => JSON.stringify({test: 'data'}),
            () => new XMLHttpRequest()
        ];
        
        operations.forEach(op => {
            try {
                op();
            } catch (e) {
                // Ignore errors
            }
        });
        
        const endTime = performance.now();
        const executionTime = endTime - startTime;
        
        if (executionTime < 50) { // Less than 50ms is acceptable
            console.log('✅ Performance: GOOD (' + executionTime.toFixed(2) + 'ms)');
        } else {
            console.log('⚠️ Performance: SLOW (' + executionTime.toFixed(2) + 'ms)');
        }
    }
    
    // Test 7: Ad Detection Accuracy
    function testAdDetectionAccuracy() {
        console.log('🔍 Testing Ad Detection Accuracy...');
        
        // Create test ad elements
        const testAds = [
            { tag: 'div', class: 'ytd-ad-slot-renderer', id: 'test-ad-1' },
            { tag: 'div', class: 'ytd-display-ad-renderer', id: 'test-ad-2' },
            { tag: 'div', class: 'advertisement', id: 'test-ad-3' }
        ];
        
        let detectedAds = 0;
        testAds.forEach(adConfig => {
            const testAd = document.createElement(adConfig.tag);
            testAd.className = adConfig.class;
            testAd.id = adConfig.id;
            testAd.textContent = 'Test Advertisement';
            document.body.appendChild(testAd);
            
            // Check if ad is hidden after a short delay
            setTimeout(() => {
                const style = window.getComputedStyle(testAd);
                if (style.display === 'none' || !document.body.contains(testAd)) {
                    detectedAds++;
                }
                testAd.remove();
                
                if (detectedAds === testAds.length) {
                    console.log('✅ Ad Detection: EXCELLENT (100% accuracy)');
                } else if (detectedAds > 0) {
                    console.log('⚠️ Ad Detection: PARTIAL (' + Math.round((detectedAds/testAds.length)*100) + '% accuracy)');
                } else {
                    console.log('❌ Ad Detection: POOR (0% accuracy)');
                }
            }, 1000);
        });
    }
    
    // Run All Tests
    function runAllTests() {
        console.log('🚀 Running Complete Test Suite...');
        console.log('=====================================');
        
        testNetworkBlocking();
        testCSSBlocking();
        testScriptletBlocking();
        testAntiDetection();
        testSmartMonitoring();
        testPerformance();
        testAdDetectionAccuracy();
        
        // Generate final report
        setTimeout(() => {
            generateTestReport();
        }, 2000);
    }
    
    // Generate Test Report
    function generateTestReport() {
        console.log('=====================================');
        console.log('📊 FINAL TEST REPORT');
        console.log('=====================================');
        
        const totalTests = Object.keys(testResults).length;
        const passedTests = Object.values(testResults).filter(result => result).length;
        const successRate = Math.round((passedTests / totalTests) * 100);
        
        console.log('Network Blocking:', testResults.networkBlocking ? '✅ PASS' : '❌ FAIL');
        console.log('CSS Blocking:', testResults.cssBlocking ? '✅ PASS' : '❌ FAIL');
        console.log('Scriptlet Blocking:', testResults.scriptletBlocking ? '✅ PASS' : '❌ FAIL');
        console.log('Anti-Detection:', testResults.antiDetection ? '✅ PASS' : '❌ FAIL');
        console.log('Smart Monitoring:', testResults.smartMonitoring ? '✅ PASS' : '❌ FAIL');
        
        console.log('=====================================');
        console.log('Overall Success Rate:', successRate + '%');
        
        if (successRate >= 80) {
            console.log('🎉 EXCELLENT! YouTube Master ad blocking is working optimally.');
        } else if (successRate >= 60) {
            console.log('⚠️ GOOD! Some improvements needed.');
        } else {
            console.log('❌ POOR! Significant issues detected.');
        }
        
        console.log('=====================================');
    }
    
    // Auto-run tests when script loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runAllTests);
    } else {
        runAllTests();
    }
    
    // Export test functions for manual testing
    window.YouTubeMasterTests = {
        runAllTests,
        testNetworkBlocking,
        testCSSBlocking,
        testScriptletBlocking,
        testAntiDetection,
        testSmartMonitoring,
        testPerformance,
        testAdDetectionAccuracy,
        generateTestReport
    };
    
})();
