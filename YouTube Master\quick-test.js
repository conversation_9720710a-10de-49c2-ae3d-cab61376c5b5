// Quick Ad Blocking Test for YouTube Master
// Run this in browser console on YouTube

(function() {
    'use strict';
    
    console.log('🧪 Quick Ad Blocking Test Starting...');
    
    // Test 1: Check if CSS rules are applied
    function testCSSBlocking() {
        console.log('🔍 Testing CSS Blocking...');
        
        const adSelectors = [
            'ytd-ad-slot-renderer',
            '.ytd-display-ad-renderer',
            '#masthead-ad',
            '.ytd-promoted-sparkles-web-renderer'
        ];
        
        let hiddenAds = 0;
        let totalAds = 0;
        
        adSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            totalAds += elements.length;
            
            elements.forEach(el => {
                const style = window.getComputedStyle(el);
                if (style.display === 'none') {
                    hiddenAds++;
                }
            });
        });
        
        console.log(`Found ${totalAds} ad elements, ${hiddenAds} hidden`);
        
        if (totalAds === 0) {
            console.log('✅ CSS Blocking: NO ADS FOUND (Perfect!)');
        } else if (hiddenAds === totalAds) {
            console.log('✅ CSS Blocking: ALL ADS HIDDEN (Excellent!)');
        } else if (hiddenAds > 0) {
            console.log('⚠️ CSS Blocking: PARTIAL (' + Math.round((hiddenAds/totalAds)*100) + '%)');
        } else {
            console.log('❌ CSS Blocking: NOT WORKING');
        }
    }
    
    // Test 2: Check if network rules are active
    function testNetworkBlocking() {
        console.log('🔍 Testing Network Blocking...');
        
        if (typeof chrome !== 'undefined' && chrome.declarativeNetRequest) {
            chrome.declarativeNetRequest.getDynamicRules().then(rules => {
                const adRules = rules.filter(rule => 
                    rule.condition.urlFilter && 
                    (rule.condition.urlFilter.includes('pagead') ||
                     rule.condition.urlFilter.includes('googlesyndication') ||
                     rule.condition.urlFilter.includes('doubleclick'))
                );
                
                if (adRules.length > 0) {
                    console.log('✅ Network Blocking: ACTIVE (' + adRules.length + ' rules)');
                } else {
                    console.log('❌ Network Blocking: NO RULES FOUND');
                }
            }).catch(() => {
                console.log('⚠️ Network Blocking: Cannot access rules (normal for content script)');
            });
        } else {
            console.log('⚠️ Network Blocking: Cannot test from content script');
        }
    }
    
    // Test 3: Check if scriptlets are working
    function testScriptlets() {
        console.log('🔍 Testing Scriptlets...');
        
        let scriptletCount = 0;
        
        // Check if global objects are modified
        if (typeof window.ytInitialPlayerResponse !== 'undefined') {
            scriptletCount++;
        }
        
        // Check if fetch is modified
        if (window.fetch.toString().length > 50) {
            scriptletCount++;
        }
        
        // Check if JSON.parse is modified
        if (JSON.parse.toString().length > 50) {
            scriptletCount++;
        }
        
        if (scriptletCount > 0) {
            console.log('✅ Scriptlets: ACTIVE (' + scriptletCount + ' modifications detected)');
        } else {
            console.log('❌ Scriptlets: NOT DETECTED');
        }
    }
    
    // Test 4: Check player visibility
    function testPlayerVisibility() {
        console.log('🔍 Testing Player Visibility...');
        
        const playerSelectors = [
            '#movie_player',
            '.html5-video-player',
            'video'
        ];
        
        let visiblePlayers = 0;
        let totalPlayers = 0;
        
        playerSelectors.forEach(selector => {
            const players = document.querySelectorAll(selector);
            totalPlayers += players.length;
            
            players.forEach(player => {
                const style = window.getComputedStyle(player);
                if (style.display !== 'none' && style.visibility !== 'hidden') {
                    visiblePlayers++;
                }
            });
        });
        
        if (totalPlayers === 0) {
            console.log('⚠️ Player: NO PLAYERS FOUND');
        } else if (visiblePlayers === totalPlayers) {
            console.log('✅ Player: ALL VISIBLE (' + visiblePlayers + '/' + totalPlayers + ')');
        } else {
            console.log('❌ Player: VISIBILITY ISSUES (' + visiblePlayers + '/' + totalPlayers + ')');
        }
    }
    
    // Test 5: Check for ad detection messages
    function testAdDetection() {
        console.log('🔍 Testing Ad Detection Messages...');
        
        const detectionSelectors = [
            'ytd-enforcement-message-view-model',
            '[data-testid="ad-blocker-detection"]',
            '.ytd-popup-container'
        ];
        
        let detectionElements = 0;
        
        detectionSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            detectionElements += elements.length;
        });
        
        if (detectionElements === 0) {
            console.log('✅ Ad Detection: NO WARNINGS FOUND');
        } else {
            console.log('⚠️ Ad Detection: ' + detectionElements + ' warning elements found');
        }
    }
    
    // Run all tests
    function runQuickTest() {
        console.log('🚀 Running Quick Test...');
        console.log('=====================================');
        
        testCSSBlocking();
        testNetworkBlocking();
        testScriptlets();
        testPlayerVisibility();
        testAdDetection();
        
        console.log('=====================================');
        console.log('✅ Quick test completed!');
        console.log('💡 If ads are still showing, try:');
        console.log('   1. Refresh the page');
        console.log('   2. Check extension settings');
        console.log('   3. Clear browser cache');
    }
    
    // Auto-run test
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runQuickTest);
    } else {
        runQuickTest();
    }
    
    // Export for manual testing
    window.YouTubeMasterQuickTest = {
        runQuickTest,
        testCSSBlocking,
        testNetworkBlocking,
        testScriptlets,
        testPlayerVisibility,
        testAdDetection
    };
    
})();
