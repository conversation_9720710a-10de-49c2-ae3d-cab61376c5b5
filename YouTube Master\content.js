﻿let settings = {
  playbackSpeed: 1.0,
  focusMode: false,
  hideComments: false,
  autoHD: false,
  muteAndSpeedupAds: true,
  darkMode: false,
  screenshotFormat: 'png',
  adBlocking: true,
  networkBlocking: true,
  cssBlocking: true,
  scriptletBlocking: true
};

let videoPlayer = null;
let originalSpeed = 1.0;
let currentLang = 'en';
let translations = {};
let currentTranscript = [];
let isTranscriptVisible = false;
let adBlockDetectionActive = false;
let showTimestamps = true;

// Advanced CSS Rules for Ad Blocking
const advancedCSSRules = [
  // YouTube Ad Containers
  '#offer-module',
  '#promotion-shelf',
  '#masthead-ad',
  '.ytd-display-ad-renderer',
  '.ytd-promoted-sparkles-web-renderer',
  '.ytd-video-masthead-ad-v3-renderer',
  '.ytd-primetime-promo-renderer',

  // Ad Slots and Renderers
  'ytd-ad-slot-renderer',
  'ytd-rich-item-renderer:has(> #content > ytd-ad-slot-renderer)',
  '.ytd-rich-item-renderer.style-scope > .ytd-rich-item-renderer > .ytd-ad-slot-renderer.style-scope',
  'ytd-item-section-renderer > .ytd-item-section-renderer > ytd-ad-slot-renderer.style-scope',

  // Watch Page Ads
  '.ytd-watch-flexy > .ytd-watch-next-secondary-results-renderer > ytd-ad-slot-renderer.ytd-watch-next-secondary-results-renderer',
  '.ytd-watch-flexy > ytd-merch-shelf-renderer > #main.ytd-merch-shelf-renderer',

  // Search and Browse Ads
  '.ytd-section-list-renderer > .ytd-item-section-renderer > ytd-search-pyv-renderer.ytd-item-section-renderer',
  '.ytd-two-column-browse-results-renderer > ytd-rich-grid-renderer > #masthead-ad.ytd-rich-grid-renderer',
  '.grid.ytd-browse > #primary > .style-scope > .ytd-rich-grid-renderer > .ytd-rich-grid-renderer > .ytd-ad-slot-renderer',

  // Shorts Ads
  '#shorts-inner-container > .ytd-shorts:has(> .ytd-reel-video-renderer > ytd-ad-slot-renderer)',
  '.ytReelMetapanelViewModelHost > .ytReelMetapanelViewModelMetapanelItem > .ytShortsSuggestedActionViewModelStaticHost',

  // Player Ads
  '.ytp-suggested-action > .ytp-suggested-action-badge',
  '.ytp-ce-element-show',
  '.ytp-cards-teaser',
  '.ytp-ce-covering-overlay',
  '.ytp-ce-element',
  '.ytp-ce-video',
  '.ytp-ce-playlist',
  '.ytp-ce-channel',

  // Overlay and Banner Ads
  '.ytd-banner-promo-renderer',
  '.ytd-statement-banner-renderer',
  '.ytd-emergency-onebox-renderer',

  // Sponsored Content
  '[aria-label*="Sponsored"]',
  '[title*="Sponsored"]',
  '.ytd-compact-promoted-item-renderer',

  // Generic Ad Patterns
  '[id*="google_ads"]',
  '[class*="google-ads"]',
  '[data-ad-slot]',
  '.ad-showing',
  '.ads-pool',
  '.advertisement'
];

// Anti-detection CSS injection
function injectAdvancedCSS() {
  if (!settings.cssBlocking) return;

  // Create multiple style elements to avoid detection
  const styleElements = [];
  const rulesPerElement = Math.ceil(advancedCSSRules.length / 3);

  for (let i = 0; i < 3; i++) {
    const style = document.createElement('style');
    style.type = 'text/css';

    // Add random attributes to avoid detection patterns
    style.setAttribute('data-yt-' + Math.random().toString(36).substr(2, 9), '');

    const startIndex = i * rulesPerElement;
    const endIndex = Math.min(startIndex + rulesPerElement, advancedCSSRules.length);
    const rules = advancedCSSRules.slice(startIndex, endIndex);

    // Obfuscate CSS with random spacing and comments
    const cssContent = rules.map(rule => {
      const randomComment = `/* ${Math.random().toString(36).substr(2, 5)} */`;
      const randomSpacing = ' '.repeat(Math.floor(Math.random() * 3));
      return `${randomSpacing}${rule}${randomSpacing}{ display: none !important; }${randomComment}`;
    }).join('\n');

    style.textContent = cssContent;
    styleElements.push(style);
  }

  // Inject styles with random delays
  styleElements.forEach((style, index) => {
    setTimeout(() => {
      if (document.head) {
        document.head.appendChild(style);
      } else {
        document.addEventListener('DOMContentLoaded', () => {
          document.head.appendChild(style);
        });
      }
    }, Math.random() * 100 + index * 50);
  });

  log('Advanced CSS rules injected');
}

// Dynamic CSS rule updates
function updateCSSRules() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === 1 && settings.cssBlocking) {
          // Check if the added node matches ad patterns
          advancedCSSRules.forEach(rule => {
            try {
              if (node.matches && node.matches(rule.replace(/\s*\{\s*display:\s*none\s*!important;\s*\}\s*/g, ''))) {
                node.style.display = 'none';
                log(`Dynamically hidden ad element: ${rule}`);
              }

              const adElements = node.querySelectorAll(rule.replace(/\s*\{\s*display:\s*none\s*!important;\s*\}\s*/g, ''));
              adElements.forEach(el => {
                el.style.display = 'none';
                log(`Dynamically hidden ad element: ${rule}`);
              });
            } catch (e) {
              // Ignore invalid selectors
            }
          });
        }
      });
    });
  });

  if (document.body) {
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  } else {
    document.addEventListener('DOMContentLoaded', () => {
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    });
  }
}

// Enable debug messages into the console
const debugMessages = true;

// Load and execute advanced scriptlets
function loadAdvancedScriptlets() {
  if (!settings.scriptletBlocking) return;

  // Inject scriptlets with anti-detection
  const script = document.createElement('script');
  script.src = chrome.runtime.getURL('scriptlets.js');
  script.onload = function() {
    this.remove();
    log('Advanced scriptlets loaded and executed');
  };

  // Inject at document start for maximum effectiveness
  if (document.documentElement) {
    document.documentElement.appendChild(script);
  } else {
    const observer = new MutationObserver((mutations, obs) => {
      if (document.documentElement) {
        document.documentElement.appendChild(script);
        obs.disconnect();
      }
    });
    observer.observe(document, { childList: true });
  }
}

// Advanced anti-detection system
function implementAntiDetection() {
  // Randomize extension behavior patterns
  const randomDelay = () => Math.random() * 200 + 100;

  // Hide extension presence
  Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined
  });

  // Spoof user agent patterns
  const originalUserAgent = navigator.userAgent;
  Object.defineProperty(navigator, 'userAgent', {
    get: () => {
      // Randomly modify user agent to avoid fingerprinting
      if (Math.random() > 0.95) {
        return originalUserAgent.replace(/Chrome\/[\d.]+/, `Chrome/${Math.floor(Math.random() * 10) + 100}.0.0.0`);
      }
      return originalUserAgent;
    }
  });

  // Prevent detection through timing analysis
  const originalSetTimeout = window.setTimeout;
  window.setTimeout = function(callback, delay, ...args) {
    // Add random jitter to timing
    const jitter = Math.random() * 10 - 5;
    return originalSetTimeout.call(this, callback, delay + jitter, ...args);
  };

  // Hide script modifications
  const originalToString = Function.prototype.toString;
  Function.prototype.toString = function() {
    if (this === window.setTimeout) {
      return 'function setTimeout() { [native code] }';
    }
    return originalToString.call(this);
  };

  log('Anti-detection measures implemented');
}

// Enhanced ad detection and removal
function enhancedAdDetection() {
  const adPatterns = [
    /advertisement/i,
    /sponsored/i,
    /promotion/i,
    /\bad[s]?\b/i,
    /google.*ads/i,
    /doubleclick/i
  ];

  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === 1) {
          // Check text content for ad patterns
          const textContent = node.textContent || '';
          const hasAdPattern = adPatterns.some(pattern => pattern.test(textContent));

          if (hasAdPattern) {
            // Check if it's likely an ad element
            const classList = Array.from(node.classList || []);
            const id = node.id || '';
            const isAdElement = classList.some(cls => /ad|sponsor|promo/i.test(cls)) ||
                              /ad|sponsor|promo/i.test(id);

            if (isAdElement) {
              node.style.display = 'none';
              log(`Removed ad element by pattern matching: ${node.tagName}`);
            }
          }

          // Check for video ads specifically
          if (node.tagName === 'VIDEO' || node.querySelector('video')) {
            const videoElement = node.tagName === 'VIDEO' ? node : node.querySelector('video');
            if (videoElement && videoElement.src && /googleads|doubleclick/i.test(videoElement.src)) {
              node.style.display = 'none';
              log('Removed video ad element');
            }
          }
        }
      });
    });
  });

  if (document.body) {
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      characterData: true
    });
  }
}

// YouTube-specific ad blocking enhancements
function youtubeSpecificBlocking() {
  // Block YouTube's ad detection scripts
  const originalCreateElement = document.createElement;
  document.createElement = function(tagName) {
    const element = originalCreateElement.call(this, tagName);

    if (tagName.toLowerCase() === 'script') {
      const originalSrcSetter = Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype, 'src').set;
      Object.defineProperty(element, 'src', {
        set: function(value) {
          // Block known ad detection scripts
          if (/pagead|googleads|doubleclick|googlesyndication/i.test(value)) {
            log(`Blocked ad script: ${value}`);
            return;
          }
          originalSrcSetter.call(this, value);
        },
        get: function() {
          return this._src || '';
        }
      });
    }

    return element;
  };

  // Intercept and modify YouTube's player configuration
  const originalParse = JSON.parse;
  JSON.parse = function(text) {
    try {
      const result = originalParse.call(this, text);

      // Remove ad-related configuration
      if (result && typeof result === 'object') {
        if (result.adPlacements) {
          delete result.adPlacements;
          log('Removed adPlacements from JSON response');
        }
        if (result.playerAds) {
          delete result.playerAds;
          log('Removed playerAds from JSON response');
        }
        if (result.adSlots) {
          delete result.adSlots;
          log('Removed adSlots from JSON response');
        }
      }

      return result;
    } catch (e) {
      return originalParse.call(this, text);
    }
  };
}

// Log messages
function log(message, level = 'log', ...args) {
  if (!debugMessages) return;
  const prefix = 'YouTubeEnhancer:';
  const logMessage = `${prefix} ${message}`;
  switch (level) {
    case 'e':
    case 'error':
      console.error(logMessage, ...args);
      break;
    case 'w':
    case 'warn':
      console.warn(logMessage, ...args);
      break;
    case 'i':
    case 'info':
      console.info(logMessage, ...args);
      break;
    case 'log':
    default:
      console.log(logMessage, ...args);
  }
}

function insertScreenshotButton() {
  const controlPanel = document.querySelector('.ytp-right-controls');
  if (!controlPanel || document.getElementById('capture-btn')) return;

  const captureBtn = document.createElement('button');
  captureBtn.id = 'capture-btn';
  captureBtn.title = translations['screenshot_tooltip']?.message || chrome.i18n.getMessage('screenshot_tooltip') || 'Take a screenshot of the video';
  captureBtn.style.cssText = `
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: none;
    border: none;
    cursor: pointer;
    margin-right: 8px;
    padding: 0;
    position: relative;
    top: -10px;
  `;

  const captureIcon = document.createElement('img');
  captureIcon.src = chrome.runtime.getURL('icons/capture-icon.png');
  captureIcon.style.cssText = `
    width: 24px;
    height: 24px;
  `;

  captureBtn.appendChild(captureIcon);
  controlPanel.insertBefore(captureBtn, controlPanel.firstChild);

  captureBtn.addEventListener('click', captureVideoFrame);
}

function insertPiPButton() {
  const controlPanel = document.querySelector('.ytp-right-controls');
  if (!controlPanel || document.getElementById('pip-toggle-btn')) return;

  const pipBtn = document.createElement('button');
  pipBtn.id = 'pip-toggle-btn';
  pipBtn.title = translations['pip_tooltip']?.message || chrome.i18n.getMessage('pip_tooltip') || 'Toggle Picture-in-Picture mode';
  pipBtn.style.cssText = `
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: none;
    border: none;
    cursor: pointer;
    margin-right: 8px;
    padding: 0;
    position: relative;
    top: -10px;
  `;

  const pipIcon = document.createElement('img');
  pipIcon.src = chrome.runtime.getURL('icons/pip-toggle-icon.png');
  pipIcon.style.cssText = `
    width: 24px;
    height: 24px;
  `;

  pipBtn.appendChild(pipIcon);
  const captureBtn = document.getElementById('capture-btn');
  if (captureBtn) {
    controlPanel.insertBefore(pipBtn, captureBtn.nextSibling);
  } else {
    controlPanel.insertBefore(pipBtn, controlPanel.firstChild);
  }

  pipBtn.addEventListener('click', () => {
    const video = getVideoElement();
    if (!video) {
      showToast('video_not_found');
      return;
    }

    if (video.readyState < 2) {
      video.addEventListener('loadedmetadata', () => {
        togglePiPMode(video);
      }, { once: true });
    } else {
      togglePiPMode(video);
    }
  });
}

function insertTranscriptButton() {
  const controlPanel = document.querySelector('.ytp-right-controls');
  if (!controlPanel || document.getElementById('transcript-toggle-btn')) return;

  const transcriptBtn = document.createElement('button');
  transcriptBtn.id = 'transcript-toggle-btn';
  transcriptBtn.title = translations['transcript_tooltip']?.message || chrome.i18n.getMessage('transcript_tooltip') || 'Show or hide video transcript';
  transcriptBtn.style.cssText = `
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: none;
    border: none;
    cursor: pointer;
    margin-right: 8px;
    padding: 0;
    position: relative;
    top: -10px;
  `;

  const transcriptIcon = document.createElement('img');
  transcriptIcon.src = chrome.runtime.getURL('icons/transcript-icon.png');
  transcriptIcon.style.cssText = `
    width: 24px;
    height: 24px;
  `;

  transcriptBtn.appendChild(transcriptIcon);
  const pipBtn = document.getElementById('pip-toggle-btn');
  if (pipBtn) {
    controlPanel.insertBefore(transcriptBtn, pipBtn.nextSibling);
  } else {
    const captureBtn = document.getElementById('capture-btn');
    if (captureBtn) {
      controlPanel.insertBefore(transcriptBtn, captureBtn.nextSibling);
    } else {
      controlPanel.insertBefore(transcriptBtn, controlPanel.firstChild);
    }
  }

  transcriptBtn.addEventListener('click', toggleTranscript);
}

function togglePiPMode(video) {
  if (document.pictureInPictureElement) {
    document.exitPictureInPicture()
      .then(() => log('Exited Picture-in-Picture mode'))
      .catch(err => log('Failed to exit PiP:', err, 'error'));
  } else {
    video.requestPictureInPicture()
      .then(() => log('Entered Picture-in-Picture mode'))
      .catch(err => log('Failed to enter PiP:', err, 'error'));
  }
}

function captureVideoFrame() {
  const video = getVideoElement();
  if (!video) {
    showToast('video_not_found');
    return;
  }

  const canvas = document.createElement('canvas');
  canvas.width = video.videoWidth;
  canvas.height = video.videoHeight;
  const ctx = canvas.getContext('2d');
  ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

  let videoTitle = document.querySelector('h1.title.ytd-video-primary-info-renderer')?.textContent || document.title;
  videoTitle = videoTitle.replace(/ - YouTube$/, '').replace(/[^a-zA-Z0-9\u0600-\u06FF\s]/g, '');
  videoTitle = videoTitle.trim().replace(/\s+/g, '_');
  const fileName = `${videoTitle}_capture.${settings.screenshotFormat}`;

  canvas.toBlob(blob => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    URL.revokeObjectURL(url);
    showToast('capture_success');
  }, `image/${settings.screenshotFormat}`);
}

function toggleTranscript() {
  isTranscriptVisible = !isTranscriptVisible;
  log('Toggling transcript, isVisible:', isTranscriptVisible);
  if (isTranscriptVisible) {
    loadAndShowTranscript();
  } else {
    removeTranscriptBox();
  }
}

loadSettings();
chrome.storage.sync.get({ language: currentLang }, async (data) => {
  currentLang = data.language;
  await loadTranslations(currentLang);
});
chrome.storage.onChanged.addListener(async (changes) => {
  if (changes.language) {
    currentLang = changes.language.newValue;
    await loadTranslations(currentLang);
  }
});

async function loadTranslations(lang) {
  try {
    const response = await fetch(chrome.runtime.getURL(`_locales/${lang}/messages.json`));
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    translations = await response.json();
  } catch (e) {
    console.error(`Failed to load translations for ${lang}:`, e);
    translations = {};
  }
}

chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'setPlaybackSpeed') {
    setVideoSpeed(request.speed);
    sendResponse({ success: true });
  } else if (request.action === 'getCurrentPosition') {
    const video = getVideoElement();
    if (video) sendResponse({ currentTime: video.currentTime });
    else sendResponse({ error: 'Video element not found' });
  } else if (request.action === 'jumpToPosition') {
    const video = getVideoElement();
    if (video) {
      video.currentTime = request.time;
      sendResponse({ success: true });
    } else sendResponse({ error: 'Video element not found' });
  } else if (request.action === 'applySettings') {
    loadSettings(applySettings);
    sendResponse({ success: true });
  } else if (request.action === 'takeScreenshot') {
    captureVideoFrame();
    sendResponse({ success: true });
  }
  return true;
});

window.addEventListener('load', initializeExtension);
document.addEventListener('yt-navigate-finish', initializeExtension);

function initializeExtension() {
  // Initialize anti-detection first
  implementAntiDetection();

  // Load advanced ad blocking systems
  if (settings.scriptletBlocking) {
    loadAdvancedScriptlets();
  }

  if (settings.cssBlocking) {
    injectAdvancedCSS();
    updateCSSRules();
  }

  // Enhanced ad detection
  enhancedAdDetection();
  youtubeSpecificBlocking();

  if (window.location.pathname === '/watch' || window.location.pathname.includes('/shorts/')) {
    waitForVideoElement().then(() => {
      applySettings();
      setupVideoEventListeners();
      startAdMonitoring();
      insertScreenshotButton();
      insertPiPButton();
      insertTranscriptButton();
      monitorAdBlockEnforcement();

      if (localStorage.getItem('adBlockReloadFlag')) {
        const video = getVideoElement();
        if (video) {
          video.currentTime = 3;
          log('Skipped first 3 seconds of video after ad block reload');
          localStorage.removeItem('adBlockReloadFlag');
        }
      }
    });
  }

  log('YouTube Master with advanced ad blocking initialized');
}

function waitForVideoElement() {
  return new Promise((resolve) => {
    const checkVideo = () => {
      const video = getVideoElement();
      if (video) resolve(video);
      else setTimeout(checkVideo, 500);
    };
    checkVideo();
  });
}

function getVideoElement() {
  videoPlayer = document.querySelector('video.html5-main-video');
  return videoPlayer;
}

function loadSettings(callback) {
  chrome.storage.sync.get({
    playbackSpeed: 1.0,
    focusMode: false,
    hideComments: false,
    autoHD: false,
    muteAndSpeedupAds: true,
    darkMode: false,
    screenshotFormat: 'png',
    adBlocking: true,
    networkBlocking: true,
    cssBlocking: true,
    scriptletBlocking: true
  }, function(data) {
    settings = data;
    if (callback) callback();
  });
}

function saveSettings() {
  chrome.storage.sync.set(settings);
}

async function loadAndShowTranscript() {
  log('loadAndShowTranscript called');
  if (!isTranscriptVisible) {
    log('Transcript not visible, skipping load');
    removeTranscriptBox();
    return;
  }

  const videoUrl = window.location.href;
  const isShorts = /youtube\.com\/shorts\//.test(videoUrl);
  const videoId = isShorts
    ? videoUrl.split('/shorts/')[1]?.split(/[/?#&]/)[0]
    : new URLSearchParams(window.location.search).get('v');

  if (!videoId) {
    log('No videoId found', 'error');
    showToast('no_video_id');
    displayTranscript([], [], null);
    return;
  }

  try {
    log('Fetching transcript for videoId:', videoId);
    const transcriptObj = await getTranscriptDict(videoUrl);
    log('Transcript loaded successfully, length:', transcriptObj.transcript.length);
    currentTranscript = transcriptObj.transcript;
    displayTranscript(transcriptObj.transcript, [], null);
  } catch (e) {
    log('Error loading transcript:', e, 'error');
    showToast('transcript_error');
    displayTranscript([], [], null);
  }
}

async function getTranscriptDict(videoUrl) {
  const isShorts = /youtube\.com\/shorts\//.test(videoUrl);
  const dataType = isShorts ? "shorts" : "regular";
  const { title, ytData, dataKey, resolvedType } = await resolveYouTubeData(videoUrl, dataType);
  const segments = await getTranscriptItems(ytData, dataKey);
  const transcript = segments.length ? createTranscriptArray(segments, resolvedType) : [];
  return { title, transcript };
}

async function resolveYouTubeData(videoUrl, initialType) {
  const dataKey = initialType === "regular" ? "ytInitialData" : "ytInitialPlayerResponse";
  const html = await fetch(videoUrl).then(res => {
    if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
    return res.text();
  });
  let ytData = extractJsonFromHtml(html, dataKey);

  let title = chrome.i18n.getMessage("untitled");
  if (dataKey === "ytInitialData") {
    title = ytData?.videoDetails?.title ||
            ytData?.playerOverlays?.playerOverlayRenderer?.videoDetails?.playerOverlayVideoDetailsRenderer?.title?.simpleText ||
            chrome.i18n.getMessage("untitled");
    const panels = ytData?.engagementPanels || [];
    const hasTranscriptPanel = panels.some(p =>
      p.engagementPanelSectionListRenderer?.content?.continuationItemRenderer?.continuationEndpoint?.getTranscriptEndpoint
    );
    if (!hasTranscriptPanel && initialType === "regular") {
      log('No transcript panel in ytInitialData, falling back to ytInitialPlayerResponse', 'warn');
      ytData = extractJsonFromHtml(html, "ytInitialPlayerResponse");
      title = ytData?.videoDetails?.title || chrome.i18n.getMessage("untitled");
      return { title, ytData, dataKey: "ytInitialPlayerResponse", resolvedType: "shorts" };
    }
  } else {
    title = ytData?.videoDetails?.title || chrome.i18n.getMessage("untitled");
  }

  return { title, ytData, dataKey, resolvedType: initialType };
}

function createTranscriptArray(items, type) {
  return type === "regular"
    ? items.map(item => getSegmentData(item))
    : items.filter(e => e.segs).map(e => getShortsSegmentData(e));
}

function getSegmentData(item) {
  const seg = item?.transcriptSegmentRenderer;
  if (!seg) return ["", ""];
  const timestamp = seg.startTimeText?.simpleText || "";
  const text = seg.snippet?.runs?.map(r => r.text).join(" ") || "";
  return [timestamp, text];
}

function getShortsSegmentData(event) {
  const timestamp = msToTimestamp(event.tStartMs);
  const text = (event.segs || []).map(seg => seg.utf8).join(" ").replace(/\n/g, " ");
  return [timestamp, text];
}

async function getTranscriptItems(ytData, dataKey) {
  if (dataKey === "ytInitialPlayerResponse") {
    const captionTracks = ytData?.captions?.playerCaptionsTracklistRenderer?.captionTracks || [];
    if (!captionTracks.length) {
      log('No caption tracks available', 'warn');
      return [];
    }

    // Prioritize default track, then auto-generated (asr), then first available track
    let selectedTrack = captionTracks.find(track => track.isDefault) ||
                       captionTracks.find(track => track.kind === 'asr') ||
                       captionTracks[0];
    
    if (!selectedTrack?.baseUrl) {
      log('No transcript URL found', 'warn');
      return [];
    }

    log(`Using ${selectedTrack.kind === 'asr' ? 'auto-generated' : 'manual'} transcript: ${selectedTrack.name?.simpleText || 'Unnamed'}`);

    try {
      const captionUrl = selectedTrack.baseUrl + "&fmt=json3";
      const json = await fetch(captionUrl).then(res => {
        if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
        return res.json();
      });
      return json.events || [];
    } catch (e) {
      log('Failed to fetch JSON transcript:', e, 'error');
      return [];
    }
  }

  const continuationParams = ytData.engagementPanels?.find(p =>
    p.engagementPanelSectionListRenderer?.content?.continuationItemRenderer?.continuationEndpoint?.getTranscriptEndpoint
  )?.engagementPanelSectionListRenderer?.content?.continuationItemRenderer?.continuationEndpoint?.getTranscriptEndpoint?.params;

  if (!continuationParams) {
    log('No continuationParams found, attempting ytInitialPlayerResponse', 'warn');
    // Fallback to ytInitialPlayerResponse
    const html = await fetch(window.location.href).then(res => res.text());
    const fallbackData = extractJsonFromHtml(html, "ytInitialPlayerResponse");
    const captionTracks = fallbackData?.captions?.playerCaptionsTracklistRenderer?.captionTracks || [];
    if (!captionTracks.length) {
      log('No caption tracks available in fallback', 'warn');
      return [];
    }

    let selectedTrack = captionTracks.find(track => track.isDefault) ||
                       captionTracks.find(track => track.kind === 'asr') ||
                       captionTracks[0];
    
    if (!selectedTrack?.baseUrl) {
      log('No transcript URL found in fallback', 'warn');
      return [];
    }

    log(`Using ${selectedTrack.kind === 'asr' ? 'auto-generated' : 'manual'} transcript in fallback: ${selectedTrack.name?.simpleText || 'Unnamed'}`);

    try {
      const captionUrl = selectedTrack.baseUrl + "&fmt=json3";
      const json = await fetch(captionUrl).then(res => {
        if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
        return res.json();
      });
      return json.events || [];
    } catch (e) {
      log('Failed to fetch JSON transcript in fallback:', e, 'error');
      return [];
    }
  }

  const hl = ytData.topbar?.desktopTopbarRenderer?.searchbox?.fusionSearchboxRenderer?.config?.webSearchboxConfig?.requestLanguage || "en";
  const clientData = ytData.responseContext?.serviceTrackingParams?.[0]?.params;
  const visitorData = ytData.responseContext?.webResponseContextExtensionData?.ytConfigData?.visitorData;

  const body = {
    context: {
      client: {
        hl,
        visitorData,
        clientName: clientData?.[0]?.value,
        clientVersion: clientData?.[1]?.value
      },
      request: { useSsl: true }
    },
    params: continuationParams
  };

  try {
    const res = await fetch("https://www.youtube.com/youtubei/v1/get_transcript?prettyPrint=false", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body)
    });
    if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
    const json = await res.json();
    return json.actions?.[0]?.updateEngagementPanelAction?.content?.transcriptRenderer
      ?.content?.transcriptSearchPanelRenderer?.body?.transcriptSegmentListRenderer?.initialSegments || [];
  } catch (e) {
    log('Failed to fetch transcript via API:', e, 'error');
    return [];
  }
}

function msToTimestamp(ms) {
  const totalSec = Math.floor(ms / 1000);
  const min = Math.floor(totalSec / 60);
  const sec = totalSec % 60;
  return `${min}:${sec.toString().padStart(2, '0')}`;
}

function extractJsonFromHtml(html, key) {
  const regexes = [
    new RegExp(`window\\["${key}"\\]\\s*=\\s*({[\\s\\S]+?})\\s*;`),
    new RegExp(`var ${key}\\s*=\\s*({[\\s\\S]+?})\\s*;`),
    new RegExp(`${key}\\s*=\\s*({[\\s\\S]+?})\\s*;`)
  ];

  for (const regex of regexes) {
    const match = html.match(regex);
    if (match && match[1]) {
      try {
        return JSON.parse(match[1]);
      } catch (err) {
        log(`Failed to parse ${key}: ${err.message}`, 'warn');
      }
    }
  }

  log(`${key} not found in HTML`, 'error');
  throw new Error(`${key} not found`);
}

function displayTranscript(transcript, captionTracks, selectedLang) {
  log('displayTranscript called, transcript length:', transcript.length);

  let transcriptContainer = document.getElementById('transcript-container');
  if (!transcriptContainer) {
    transcriptContainer = document.createElement('div');
    transcriptContainer.id = 'transcript-container';
    transcriptContainer.style.cssText = `
      width: 100%;
      margin-top: 10px;
      margin-bottom: 10px;
    `;
    const belowSection = document.querySelector('#below') || document.querySelector('#panels');
    if (belowSection) {
      log('Inserting transcript container in #below or #panels');
      belowSection.insertBefore(transcriptContainer, belowSection.firstChild);
    } else {
      log('No #below or #panels found, appending to body', 'warn');
      const primarySection = document.querySelector('#primary');
      if (primarySection) {
        primarySection.appendChild(transcriptContainer);
      } else {
        document.body.appendChild(transcriptContainer);
      }
    }
  }

  let transcriptBox = document.getElementById('youtube-enhancer-transcript');
  if (!transcriptBox) {
    transcriptBox = document.createElement('div');
    transcriptBox.id = 'youtube-enhancer-transcript';
    transcriptBox.style.cssText = `
      background-color: ${settings.darkMode ? '#1f1f1f' : '#fff'};
      color: ${settings.darkMode ? '#d2cfcf' : '#333'};
      padding: 15px;
      border-radius: 8px;
      max-height: 300px;
      overflow-y: auto;
      font-size: 14px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      width: 100%;
      box-sizing: border-box;
      direction: ${currentLang === 'ar' ? 'rtl' : 'ltr'};
    `;
    transcriptContainer.appendChild(transcriptBox);
    log('Transcript box created');
  }

  const controlsContainer = document.createElement('div');
  controlsContainer.style.cssText = `
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    direction: ${currentLang === 'ar' ? 'rtl' : 'ltr'};
    align-items: center;
  `;

  const timestampButton = document.createElement('button');
  timestampButton.id = 'toggle-timestamp-btn';
  timestampButton.textContent = (showTimestamps ? translations['hide_timestamps'] : translations['show_timestamps'])?.message ||
    chrome.i18n.getMessage(showTimestamps ? 'hide_timestamps' : 'show_timestamps') || (showTimestamps ? 'Hide Timestamps' : 'Show Timestamps');
  timestampButton.style.cssText = `
    padding: 5px 10px;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  `;
  timestampButton.addEventListener('click', () => {
    showTimestamps = !showTimestamps;
    timestampButton.textContent = (showTimestamps ? translations['hide_timestamps'] : translations['show_timestamps'])?.message ||
      chrome.i18n.getMessage(showTimestamps ? 'hide_timestamps' : 'show_timestamps') || (showTimestamps ? 'Hide Timestamps' : 'Show Timestamps');
    displayTranscript(currentTranscript, [], null);
  });

  const exportButton = document.createElement('button');
  exportButton.id = 'export-transcript-btn';
  exportButton.textContent = translations['export_transcript']?.message || chrome.i18n.getMessage('export_transcript') || 'Export Transcript';
  exportButton.disabled = transcript.length === 0;
  exportButton.style.cssText = `
    padding: 5px 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: ${transcript.length ? 'pointer' : 'not-allowed'};
  `;
  exportButton.addEventListener('click', exportTranscript);

  const copyButton = document.createElement('button');
  copyButton.id = 'copy-transcript-btn';
  copyButton.textContent = translations['copy_transcript']?.message || chrome.i18n.getMessage('copy_transcript') || 'Copy Transcript';
  copyButton.disabled = transcript.length === 0;
  copyButton.style.cssText = `
    padding: 5px 10px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: ${transcript.length ? 'pointer' : 'not-allowed'};
  `;
  copyButton.addEventListener('click', copyTranscript);

  controlsContainer.appendChild(timestampButton);
  controlsContainer.appendChild(exportButton);
  controlsContainer.appendChild(copyButton);

  transcriptBox.innerHTML = '';
  transcriptBox.appendChild(controlsContainer);
  if (transcript.length === 0) {
    transcriptBox.insertAdjacentHTML('beforeend', `
      <p style="color: ${settings.darkMode ? '#d2cfcf' : '#666'}; text-align: center;">
        ${translations['no_transcript_available']?.message || chrome.i18n.getMessage('no_transcript_available') || 'No transcript available'}
      </p>
    `);
  } else {
    transcriptBox.insertAdjacentHTML('beforeend', transcript.map(entry => `
      <p style="margin: 5px 0;">${showTimestamps && entry[0] ? `(${entry[0]}) ` : ''}${entry[1]}</p>
    `).join(''));
  }

  log('Transcript box updated in #below or #panels');
}

function copyTranscript() {
  if (!currentTranscript.length) {
    log('No transcript to copy', 'warn');
    showToast('no_transcript_available');
    return;
  }

  const textContent = currentTranscript.map(entry => 
    showTimestamps && entry[0] ? `(${entry[0]}) ${entry[1]}` : entry[1]
  ).join('\n');

  navigator.clipboard.writeText(textContent)
    .then(() => {
      log('Transcript copied to clipboard');
      showToast('transcript_copied');
    })
    .catch(() => {
      const textarea = document.createElement('textarea');
      textarea.value = textContent;
      textarea.style.position = 'fixed';
      textarea.style.opacity = 0;
      document.body.appendChild(textarea);
      textarea.focus();
      textarea.select();
      try {
        document.execCommand('copy');
        log('Transcript copied using fallback');
        showToast('transcript_copied');
      } catch (err) {
        log('Failed to copy transcript:', err, 'error');
        showToast('transcript_copy_failed');
      } finally {
        document.body.removeChild(textarea);
      }
    });
}

function exportTranscript() {
  if (!currentTranscript.length) {
    log('No transcript to export', 'warn');
    return;
  }

  const textContent = currentTranscript.map(entry => 
    showTimestamps && entry[0] ? `(${entry[0]}) ${entry[1]}` : entry[1]
  ).join('\n');
  const blob = new Blob([textContent], { type: 'text/plain' });

  let videoTitle = document.querySelector('h1.title.ytd-video-primary-info-renderer')?.textContent || document.title;
  videoTitle = videoTitle.replace(/ - YouTube$/, '').replace(/[^a-zA-Z0-9\u0600-\u06FF\s]/g, '');
  videoTitle = videoTitle.trim().replace(/\s+/g, '_');
  const fileName = `${videoTitle}_transcript.txt`;

  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.click();
  URL.revokeObjectURL(url);
  log('Transcript exported as:', fileName);
}

function removeTranscriptBox() {
  const transcriptContainer = document.getElementById('transcript-container');
  if (transcriptContainer) {
    log('Removing transcript container');
    transcriptContainer.remove();
  }
}

function applySettings() {
  setVideoSpeed(settings.playbackSpeed);
  applyFocusMode();
  applyHideComments();
  applyAutoHD();
  applyDarkMode();
}

function setupVideoEventListeners() {
  const video = getVideoElement();
  if (!video) return;
}

function speedUpAndMuteAds() {
  log('speedUpAndMuteAds()');
  let isAdFound = false;
  let videoPlayback = 1;

  setInterval(() => {
    if (!settings.muteAndSpeedupAds || adBlockDetectionActive) return;
    const video = getVideoElement();
    const ad = document.querySelector('.ad-showing');
    if (ad) {
      if (!isAdFound) {
        isAdFound = true;
        log('Found Ad');
      }
      if (video) {
        video.playbackRate = 10;
        video.volume = 0;
        video.play();
      }
    } else {
      if (isAdFound) {
        isAdFound = false;
        if (video && video.playbackRate === 10) {
          video.playbackRate = videoPlayback;
          video.volume = 1;
        }
        log('Ad ended, restored playback');
      }
      if (video) {
        videoPlayback = video.playbackRate;
      }
    }
  }, 50);
}

function startAdMonitoring() {
  speedUpAndMuteAds();
}

function detectAdBlockEnforcement() {
  return document.querySelector('ytd-enforcement-message-view-model[in-player]');
}

// Advanced Smart Monitoring System
function smartAdBlockMonitoring() {
  let detectionAttempts = 0;
  let lastDetectionTime = 0;
  const maxDetectionAttempts = 3;
  const detectionCooldown = 30000; // 30 seconds

  // Enhanced detection patterns
  const detectionSelectors = [
    'ytd-enforcement-message-view-model[in-player]',
    '[data-testid="ad-blocker-detection"]',
    '.ytd-popup-container:has([aria-label*="ad blocker"])',
    '.yt-alert-message:contains("ad blocker")',
    '.ytd-message-renderer:has([aria-label*="ads"])',
    '[class*="adblock"][class*="warning"]',
    '[id*="adblock"][id*="detection"]'
  ];

  // Behavioral detection patterns
  const behavioralPatterns = [
    () => document.querySelectorAll('video').length > 1 && document.querySelectorAll('.ad-showing').length === 0,
    () => window.location.href.includes('&adblock=') || window.location.href.includes('&ab='),
    () => document.title.includes('Ad blocker') || document.title.includes('Ads blocked'),
    () => {
      const scripts = Array.from(document.scripts);
      return scripts.some(script => script.src.includes('adblock') || script.textContent.includes('adblock'));
    }
  ];

  const checkAdvancedDetection = () => {
    const now = Date.now();

    // Skip if in cooldown period
    if (now - lastDetectionTime < detectionCooldown) {
      return;
    }

    let detectionFound = false;

    // Check DOM-based detection
    for (const selector of detectionSelectors) {
      try {
        if (document.querySelector(selector)) {
          detectionFound = true;
          log(`Detection found via selector: ${selector}`, 'warn');
          break;
        }
      } catch (e) {
        // Ignore invalid selectors
      }
    }

    // Check behavioral patterns
    if (!detectionFound) {
      for (const pattern of behavioralPatterns) {
        try {
          if (pattern()) {
            detectionFound = true;
            log('Detection found via behavioral pattern', 'warn');
            break;
          }
        } catch (e) {
          // Ignore pattern errors
        }
      }
    }

    if (detectionFound) {
      detectionAttempts++;
      lastDetectionTime = now;

      log(`Ad block detection attempt ${detectionAttempts}/${maxDetectionAttempts}`, 'warn');

      if (detectionAttempts >= maxDetectionAttempts) {
        handleDetectionEvasion();
        detectionAttempts = 0; // Reset counter
      } else {
        // Temporary evasion measures
        temporaryEvasion();
      }
    }
  };

  // Temporary evasion without reload
  const temporaryEvasion = () => {
    adBlockDetectionActive = true;

    // Temporarily disable some blocking features
    const tempSettings = { ...settings };
    settings.cssBlocking = false;
    settings.scriptletBlocking = false;

    log('Temporarily disabled some blocking features', 'warn');

    setTimeout(() => {
      settings = tempSettings;
      adBlockDetectionActive = false;
      log('Re-enabled blocking features', 'info');
    }, 10000); // 10 seconds
  };

  // Advanced evasion with intelligent reload
  const handleDetectionEvasion = () => {
    const lastReload = localStorage.getItem('lastAdBlockReload');
    const now = Date.now();

    // Prevent frequent reloads
    if (!lastReload || now - parseInt(lastReload) > 60000) { // 1 minute minimum
      log('Implementing advanced evasion strategy', 'warn');

      // Store current video time
      const video = getVideoElement();
      if (video && video.currentTime > 0) {
        localStorage.setItem('videoTime', video.currentTime.toString());
      }

      // Clear detection traces
      clearDetectionTraces();

      // Set reload flag and timestamp
      localStorage.setItem('lastAdBlockReload', now.toString());
      localStorage.setItem('adBlockReloadFlag', 'true');

      // Reload with random delay to avoid pattern detection
      setTimeout(() => {
        window.location.reload();
      }, Math.random() * 2000 + 1000);
    } else {
      log('Skipping reload due to recent reload', 'info');
      temporaryEvasion();
    }
  };

  // Clear any detection traces
  const clearDetectionTraces = () => {
    // Remove detection-related elements
    detectionSelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => el.remove());
      } catch (e) {
        // Ignore errors
      }
    });

    // Clear suspicious localStorage entries
    const suspiciousKeys = Object.keys(localStorage).filter(key =>
      /adblock|detection|warning/i.test(key)
    );
    suspiciousKeys.forEach(key => {
      if (!key.includes('lastAdBlockReload') && !key.includes('adBlockReloadFlag')) {
        localStorage.removeItem(key);
      }
    });
  };

  // Restore video position after reload
  const restoreVideoPosition = () => {
    const savedTime = localStorage.getItem('videoTime');
    if (savedTime && localStorage.getItem('adBlockReloadFlag')) {
      const video = getVideoElement();
      if (video) {
        const time = parseFloat(savedTime);
        video.currentTime = Math.max(0, time - 2); // Go back 2 seconds for safety
        log(`Restored video position to ${time}s`);
        localStorage.removeItem('videoTime');
      }
    }
  };

  // Initialize monitoring
  const startMonitoring = () => {
    // Check for detection every 2 seconds with random jitter
    setInterval(() => {
      setTimeout(checkAdvancedDetection, Math.random() * 1000);
    }, 2000);

    // Restore video position if needed
    restoreVideoPosition();
  };

  startMonitoring();
}

function monitorAdBlockEnforcement() {
  // Use the new smart monitoring system
  smartAdBlockMonitoring();
}

function setVideoSpeed(speed) {
  const video = getVideoElement();
  if (video) {
    video.playbackRate = speed;
    settings.playbackSpeed = speed;
    showToast('playback_speed_set', { speed: speed });
  }
}

function applyFocusMode() {
  const style = document.getElementById('youtube-enhancer-focus-mode') || document.createElement('style');
  style.id = 'youtube-enhancer-focus-mode';
  style.textContent = settings.focusMode ? `
    ytd-watch-next-secondary-results-renderer, #secondary { display: none !important; }
    #columns.ytd-watch-flexy, #primary.ytd-watch-flexy { max-width: 100% !important; }
    .ytp-endscreen-content { display: none !important; }
  ` : '';
  if (!style.parentNode) document.head.appendChild(style);
}

function applyHideComments() {
  const style = document.getElementById('youtube-enhancer-hide-comments') || document.createElement('style');
  style.id = 'youtube-enhancer-hide-comments';
  style.textContent = settings.hideComments ? `ytd-comments { display: none !important; }` : '';
  if (!style.parentNode) document.head.appendChild(style);
}

function applyAutoHD() {
  if (settings.autoHD) {
    setTimeout(() => {
      const settingsButton = document.querySelector('.ytp-settings-button');
      if (settingsButton) {
        settingsButton.click();
        setTimeout(() => {
          const qualityMenuItem = Array.from(document.querySelectorAll('.ytp-menuitem'))
            .find(item => item.textContent.includes('الجودة') || item.textContent.includes('Quality'));
          if (qualityMenuItem) {
            qualityMenuItem.click();
            setTimeout(() => {
              const qualityOptions = document.querySelectorAll('.ytp-menuitem');
              if (qualityOptions.length > 0) qualityOptions[1].click();
            }, 300);
          } else settingsButton.click();
        }, 300);
      }
    }, 1000);
  }
}

function applyDarkMode() {
  const style = document.getElementById('youtube-enhancer-dark-mode') || document.createElement('style');
  style.id = 'youtube-enhancer-dark-mode';
  style.textContent = settings.darkMode ? `
    body, ytd-app { background-color: #121212 !important; }
    * { color: #d2cfcf !important; }
    ytd-masthead { background-color: #1f1f1f !important; }
    .ytp-chrome-top, .ytp-chrome-bottom { background-color: #1f1f1f !important; }
    ytd-thumbnail, ytd-playlist-thumbnail, .ytp-videowall-still { border-color: #606060 !important; }
  ` : '';
  if (!style.parentNode) document.head.appendChild(style);
}

function showToast(messageKey, params = {}) {
  let toast = document.getElementById('youtube-enhancer-toast');
  if (!toast) {
    toast = document.createElement('div');
    toast.id = 'youtube-enhancer-toast';
    document.body.appendChild(toast);
    const style = document.createElement('style');
    style.textContent = `
      #youtube-enhancer-toast {
        position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.8); color: white; padding: 10px 20px;
        border-radius: 4px; z-index: 9999; font-size: 14px; max-width: 80%;
        text-align: center; transition: opacity 0.3s; opacity: 0;
        direction: ${currentLang === 'ar' ? 'rtl' : 'ltr'};
      }
    `;
    document.head.appendChild(style);
  }
  let message = translations[messageKey]?.message || chrome.i18n.getMessage(messageKey) || messageKey;
  if (params.speed) {
    message = message.replace('$SPEED$', params.speed);
  }
  toast.textContent = message;
  toast.style.opacity = '1';
  setTimeout(() => toast.style.opacity = '0', 3000);
}

translations['capture_success'] = { message: 'Frame captured successfully' };
translations['video_not_found'] = { message: 'Video element not found' };
translations['no_video_id'] = { message: 'No video ID found' };
translations['no_transcript_available'] = { message: 'No transcript available for this video' };
translations['transcript_error'] = { message: 'Failed to load transcript' };
translations['transcript_copied'] = { message: 'Transcript copied to clipboard' };
translations['transcript_copy_failed'] = { message: 'Failed to copy transcript' };
translations['copy_transcript'] = { message: 'Copy Transcript' };
translations['show_timestamps'] = { message: 'Show Timestamps' };
translations['hide_timestamps'] = { message: 'Hide Timestamps' };