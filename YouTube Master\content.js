// YouTube Master - Simplified Content Script
console.log('YouTube Master: Content script loading...');

let settings = {
  playbackSpeed: 1.0,
  focusMode: false,
  hideComments: false,
  autoHD: false,
  muteAndSpeedupAds: true,
  darkMode: false,
  screenshotFormat: 'png',
  adBlocking: true,
  networkBlocking: true,
  cssBlocking: true,
  scriptletBlocking: true
};

let videoPlayer = null;
let currentLang = 'en';
let backgroundReady = false;

// Debug logging
const debugMessages = true;
function log(message, level = 'info') {
  if (debugMessages) {
    console[level](`YouTube Master: ${message}`);
  }
}

// Get video element
function getVideoElement() {
  return document.querySelector('video') || 
         document.querySelector('#movie_player video') ||
         document.querySelector('.html5-video-player video');
}

// Wait for video element
function waitForVideoElement() {
  return new Promise((resolve) => {
    const checkForVideo = () => {
      const video = getVideoElement();
      if (video) {
        resolve(video);
      } else {
        setTimeout(checkForVideo, 100);
      }
    };
    checkForVideo();
  });
}

// Initialize background communication
function initializeBackgroundCommunication() {
  chrome.runtime.sendMessage({ action: 'PAGE_READY' }, (response) => {
    if (response && response.ads) {
      settings.adBlocking = response.ads;
      backgroundReady = true;
      log('Background script communication established');
    }
  });
  
  // Ping background script periodically
  setInterval(() => {
    chrome.runtime.sendMessage({ action: 'PING' }, (response) => {
      if (!response) {
        log('Background script not responding', 'warn');
      }
    });
  }, 30000);
}

// Simple ad detection and skipping
function detectAndSkipAds() {
  if (!settings.muteAndSpeedupAds) return;
  
  const video = getVideoElement();
  if (!video) return;
  
  // Check for ad indicators
  const adIndicators = [
    '.ytp-ad-skip-button',
    '.ytp-ad-text',
    '.video-ads',
    '[class*="ad-showing"]',
    '.ytp-ad-player-overlay'
  ];
  
  const isAd = adIndicators.some(selector => document.querySelector(selector));
  
  if (isAd) {
    // Mute and speed up ad
    video.muted = true;
    video.playbackRate = 16;
    
    // Try to skip
    const skipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
    if (skipButton && skipButton.offsetParent !== null) {
      skipButton.click();
      log('Ad skipped');
    }
  } else {
    // Restore normal playback
    if (video.muted && video.playbackRate > 2) {
      video.muted = false;
      video.playbackRate = settings.playbackSpeed;
    }
  }
}

// Apply basic settings
function applySettings() {
  const video = getVideoElement();
  if (!video) return;
  
  // Apply playback speed
  if (video.playbackRate !== settings.playbackSpeed) {
    video.playbackRate = settings.playbackSpeed;
  }
  
  // Apply focus mode
  if (settings.focusMode) {
    document.body.classList.add('focus-mode');
  } else {
    document.body.classList.remove('focus-mode');
  }
  
  // Hide comments
  if (settings.hideComments) {
    const comments = document.querySelector('#comments');
    if (comments) comments.style.display = 'none';
  }
  
  // Auto HD
  if (settings.autoHD) {
    const settingsButton = document.querySelector('.ytp-settings-button');
    if (settingsButton) {
      // Try to set quality to highest available
      setTimeout(() => {
        settingsButton.click();
        setTimeout(() => {
          const qualityButton = document.querySelector('.ytp-quality-menu button');
          if (qualityButton) qualityButton.click();
        }, 100);
      }, 1000);
    }
  }
}

// Setup video event listeners
function setupVideoEventListeners() {
  const video = getVideoElement();
  if (!video) return;
  
  video.addEventListener('loadstart', () => {
    log('Video loading started');
    applySettings();
  });
  
  video.addEventListener('canplay', () => {
    log('Video can play');
    applySettings();
  });
  
  video.addEventListener('play', () => {
    log('Video playing');
    detectAndSkipAds();
  });
  
  video.addEventListener('timeupdate', () => {
    detectAndSkipAds();
  });
}

// Load settings from storage
function loadSettings(callback) {
  chrome.storage.sync.get({
    playbackSpeed: 1.0,
    focusMode: false,
    hideComments: false,
    autoHD: false,
    muteAndSpeedupAds: true,
    darkMode: false,
    screenshotFormat: 'png',
    adBlocking: true,
    networkBlocking: true,
    cssBlocking: true,
    scriptletBlocking: true
  }, function(data) {
    settings = data;
    
    // Force enable ad blocking
    if (settings.adBlocking === undefined) settings.adBlocking = true;
    if (settings.networkBlocking === undefined) settings.networkBlocking = true;
    if (settings.cssBlocking === undefined) settings.cssBlocking = true;
    if (settings.scriptletBlocking === undefined) settings.scriptletBlocking = true;
    
    log('Settings loaded - Ad blocking enabled: ' + settings.adBlocking);
    if (callback) callback();
  });
}

// Initialize extension
function initializeExtension() {
  log('Initializing YouTube Master...');
  
  // Initialize background communication
  initializeBackgroundCommunication();
  
  // Start ad detection
  setInterval(detectAndSkipAds, 1000);
  
  if (window.location.pathname === '/watch' || window.location.pathname.includes('/shorts/')) {
    waitForVideoElement().then(() => {
      applySettings();
      setupVideoEventListeners();
      
      if (localStorage.getItem('adBlockReloadFlag')) {
        const video = getVideoElement();
        if (video) {
          video.currentTime = 3;
          log('Skipped first 3 seconds of video after ad block reload');
          localStorage.removeItem('adBlockReloadFlag');
        }
      }
    });
  }
  
  log('YouTube Master initialized - background script handles ad blocking');
}

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'applySettings') {
    loadSettings(() => {
      applySettings();
      sendResponse({ success: true });
    });
  } else if (request.action === 'setPlaybackSpeed') {
    settings.playbackSpeed = request.speed;
    const video = getVideoElement();
    if (video) {
      video.playbackRate = request.speed;
    }
    sendResponse({ success: true });
  }
  return true;
});

// Start the extension
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    loadSettings(initializeExtension);
  });
} else {
  loadSettings(initializeExtension);
}

log('YouTube Master content script loaded');
