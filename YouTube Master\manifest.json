{"update_url": "https://clients2.google.com/service/update2/crx", "manifest_version": 3, "name": "__MSG_extension_name__", "version": "2.0.7", "description": "__MSG_extension_description__", "default_locale": "en", "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "permissions": ["storage", "tabs", "activeTab", "declarativeNetRequest", "scripting", "webNavigation"], "host_permissions": ["https://www.youtube.com/*", "https://youtube.com/*", "https://*.googlevideo.com/*", "https://*.googleads.com/*", "https://*.doubleclick.net/*", "https://*.googlesyndication.com/*"], "content_scripts": [{"matches": ["https://www.youtube.com/*", "https://youtube.com/*"], "js": ["content.js"], "run_at": "document_start", "all_frames": true}], "web_accessible_resources": [{"resources": ["_locales/*/*.json", "icons/pip-toggle-icon.png", "icons/capture-icon.png", "icons/transcript-icon.png", "scriptlets.js"], "matches": ["https://www.youtube.com/*", "https://youtube.com/*"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "background": {"service_worker": "background.js"}, "declarative_net_request": {"rule_resources": []}}