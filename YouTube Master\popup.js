﻿document.addEventListener('DOMContentLoaded', async function() {
  let currentLang = 'en';
  let translations = {};

  async function loadTranslations(lang) {
    try {
      const response = await fetch(chrome.runtime.getURL(`_locales/${lang}/messages.json`));
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      translations = await response.json();
    } catch (e) {
      console.error(`Failed to load translations for ${lang}:`, e);
      translations = {};
    }
    applyTranslations();
  }

  function applyTranslations() {
    document.querySelectorAll('[data-i18n]').forEach(element => {
      const key = element.getAttribute('data-i18n');
      const translation = translations[key] ? translations[key].message : chrome.i18n.getMessage(key);
      element.textContent = translation || key;
    });
  }

  chrome.storage.sync.get({ language: currentLang }, async (settings) => {
    currentLang = settings.language;
    document.getElementById('language-select').value = currentLang;
    document.documentElement.dir = currentLang === 'ar' ? 'rtl' : 'ltr';
    await loadTranslations(currentLang);
  });

  document.getElementById('language-select').addEventListener('change', async function() {
    const newLang = this.value;
    chrome.storage.sync.set({ language: newLang }, async () => {
      currentLang = newLang;
      document.documentElement.dir = newLang === 'ar' ? 'rtl' : 'ltr';
      await loadTranslations(newLang);
    });
  });

  chrome.storage.sync.get({
    playbackSpeed: 1.0,
    focusMode: false,
    hideComments: false,
    autoHD: false,
    muteAndSpeedupAds: false,
    darkMode: false,
    adBlocking: true,
    networkBlocking: true,
    cssBlocking: true,
    scriptletBlocking: true,
    bookmarks: {},
    favorites: []
  }, function(settings) {
    document.getElementById('custom-speed').value = settings.playbackSpeed;
    document.querySelector(`.speed-controls button[id="speed-${settings.playbackSpeed}"]`)?.classList.add('active');
    document.getElementById('focus-mode').checked = settings.focusMode;
    document.getElementById('hide-comments').checked = settings.hideComments;
    document.getElementById('auto-hd').checked = settings.autoHD;
    document.getElementById('mute-and-speedup-ads').checked = settings.muteAndSpeedupAds;
    document.getElementById('dark-mode').checked = settings.darkMode;

    // Ad blocking settings
    document.getElementById('ad-blocking').checked = settings.adBlocking;
    document.getElementById('network-blocking').checked = settings.networkBlocking;
    document.getElementById('css-blocking').checked = settings.cssBlocking;
    document.getElementById('scriptlet-blocking').checked = settings.scriptletBlocking;

    updateBookmarksList(settings.bookmarks);
    updateFavoritesList(settings.favorites);
  });

  const speedButtons = document.querySelectorAll('.speed-controls button');
  speedButtons.forEach(button => {
    if (button.id !== 'custom-speed-btn') {
      button.addEventListener('click', function() {
        speedButtons.forEach(btn => btn.classList.remove('active'));
        this.classList.add('active');
        const speed = parseFloat(this.id.replace('speed-', ''));
        document.getElementById('custom-speed').value = speed;
        applyPlaybackSpeed(speed);
      });
    }
  });

  document.getElementById('custom-speed-btn').addEventListener('click', function() {
    const customSpeed = parseFloat(document.getElementById('custom-speed').value);
    if (customSpeed >= 0.1 && customSpeed <= 5) applyPlaybackSpeed(customSpeed);
  });

  // Ad Blocking event listeners
  document.getElementById('ad-blocking').addEventListener('change', function() {
    chrome.storage.sync.set({ adBlocking: this.checked });
    chrome.runtime.sendMessage({ action: 'updateSettings', settings: { adBlocking: this.checked } });
    sendMessageToActiveTab({ action: 'applySettings' });
  });

  document.getElementById('network-blocking').addEventListener('change', function() {
    chrome.storage.sync.set({ networkBlocking: this.checked });
    chrome.runtime.sendMessage({ action: 'updateSettings', settings: { networkBlocking: this.checked } });
    sendMessageToActiveTab({ action: 'applySettings' });
  });

  document.getElementById('css-blocking').addEventListener('change', function() {
    chrome.storage.sync.set({ cssBlocking: this.checked });
    sendMessageToActiveTab({ action: 'applySettings' });
  });

  document.getElementById('scriptlet-blocking').addEventListener('change', function() {
    chrome.storage.sync.set({ scriptletBlocking: this.checked });
    sendMessageToActiveTab({ action: 'applySettings' });
  });

  document.getElementById('add-bookmark').addEventListener('click', addCurrentPositionBookmark);
  document.getElementById('add-favorite').addEventListener('click', addCurrentVideoToFavorites);
  document.getElementById('save-settings').addEventListener('click', () => saveSettings());
  document.getElementById('reset-settings').addEventListener('click', () => {
    resetSettings();
    showNotification('settings_reset');
  });

  document.getElementById('donateBtn').addEventListener('click', function() {
    const donateOptions = document.getElementById('donateOptions');
    donateOptions.classList.toggle('hidden');
  });

  const donateOptions = document.querySelectorAll('.donate-option');
  donateOptions.forEach(option => {
    option.addEventListener('click', function() {
      const amount = this.getAttribute('data-amount');
      let donateAmount = amount;

      if (amount === 'custom') {
        const customAmount = prompt(translations['donate_custom_amount']?.message || chrome.i18n.getMessage('donate_custom_amount') || 'Custom Amount');
        if (customAmount && !isNaN(customAmount) && parseFloat(customAmount) > 0) {
          donateAmount = customAmount;
        } else {
          return;
        }
      }

      const paypalUrl = `https://paypal.me/AElshenawy/${donateAmount}`;
      window.open(paypalUrl, '_blank');
    });
  });

  function showNotification(messageKey) {
    const notification = document.createElement('div');
    const message = translations[messageKey]?.message || chrome.i18n.getMessage(messageKey) || messageKey;
    notification.textContent = message;
    notification.style.cssText = 'position: fixed; bottom: 10px; left: 50%; transform: translateX(-50%); padding: 8px 16px; background-color: #333; color: white; border-radius: 4px; z-index: 1000;';
    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 3000);
  }

  function sendMessageToActiveTab(message) {
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      if (tabs[0] && tabs[0].url.includes('youtube.com')) {
        chrome.tabs.sendMessage(tabs[0].id, message, function(response) {
          if (chrome.runtime.lastError) {
            console.warn('Failed to send message:', chrome.runtime.lastError.message);
          }
        });
      }
    });
  }

  function applyPlaybackSpeed(speed) {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs[0] && tabs[0].url.includes('youtube.com/watch')) {
        chrome.tabs.sendMessage(tabs[0].id, { action: 'setPlaybackSpeed', speed: speed }, function(response) {
          if (chrome.runtime.lastError) {
            console.warn('Failed to send message:', chrome.runtime.lastError.message);
          } else if (response && response.success) {
            chrome.storage.sync.set({playbackSpeed: speed});
          }
        });
      }
    });
  }

  function addCurrentPositionBookmark() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs[0] && tabs[0].url.includes('youtube.com/watch')) {
        chrome.tabs.sendMessage(tabs[0].id, { action: 'getCurrentPosition' }, function(response) {
          if (response && response.currentTime !== undefined) {
            const videoId = new URL(tabs[0].url).searchParams.get('v');
            const title = tabs[0].title.replace(' - YouTube', '');
            chrome.storage.sync.get({bookmarks: {}}, function(data) {
              if (!data.bookmarks[videoId]) data.bookmarks[videoId] = { title: title, positions: [] };
              const timestamp = Math.floor(response.currentTime);
              const formattedTime = formatTime(timestamp);
              data.bookmarks[videoId].positions.push({ time: timestamp, label: formattedTime, addedAt: new Date().toISOString() });
              chrome.storage.sync.set({bookmarks: data.bookmarks}, () => {
                updateBookmarksList(data.bookmarks);
                showNotification('bookmark_added');
              });
            });
          } else if (chrome.runtime.lastError) {
            console.warn('Failed to get position:', chrome.runtime.lastError.message);
          }
        });
      }
    });
  }

  function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return hours > 0 ? `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}` : `${minutes}:${secs.toString().padStart(2, '0')}`;
  }

  function updateBookmarksList(bookmarks) {
    const bookmarksList = document.getElementById('bookmarks-list');
    bookmarksList.innerHTML = '';
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs[0] && tabs[0].url.includes('youtube.com/watch')) {
        const videoId = new URL(tabs[0].url).searchParams.get('v');
        if (bookmarks[videoId] && bookmarks[videoId].positions.length > 0) {
          bookmarks[videoId].positions.forEach((bookmark, index) => {
            const bookmarkItem = document.createElement('div');
            bookmarkItem.className = 'bookmark-item';
            bookmarkItem.innerHTML = `
              <span class="bookmark-title">${bookmark.label}</span>
              <div class="bookmark-actions">
                <button style="background-color: #2196F3; color: white;">${translations['open']?.message || chrome.i18n.getMessage('open') || 'Open'}</button>
                <button style="background-color: #f44336; color: white;">${translations['delete']?.message || chrome.i18n.getMessage('delete') || 'Delete'}</button>
              </div>
            `;
            bookmarksList.appendChild(bookmarkItem);
            bookmarkItem.querySelector('button:nth-child(1)').addEventListener('click', () => {
              chrome.tabs.sendMessage(tabs[0].id, { action: 'jumpToPosition', time: bookmark.time }, function(response) {
                if (chrome.runtime.lastError) {
                  console.warn('Failed to jump to position:', chrome.runtime.lastError.message);
                }
              });
            });
            bookmarkItem.querySelector('button:nth-child(2)').addEventListener('click', () => {
              bookmarks[videoId].positions.splice(index, 1);
              chrome.storage.sync.set({bookmarks: bookmarks}, () => bookmarkItem.remove());
            });
          });
        } else {
          const noBookmarksMessage = translations['no_bookmarks']?.message || chrome.i18n.getMessage('no_bookmarks') || 'no_bookmarks';
          bookmarksList.innerHTML = `<div style="color: #666; text-align: center; padding: 10px;">${noBookmarksMessage}</div>`;
        }
      }
    });
  }

  function addCurrentVideoToFavorites() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs[0] && tabs[0].url.includes('youtube.com/watch')) {
        const videoId = new URL(tabs[0].url).searchParams.get('v');
        const title = tabs[0].title.replace(' - YouTube', '');
        chrome.storage.sync.get({favorites: []}, function(data) {
          if (!data.favorites.some(fav => fav.id === videoId)) {
            data.favorites.push({ id: videoId, title: title });
            chrome.storage.sync.set({favorites: data.favorites}, () => {
              updateFavoritesList(data.favorites);
              showNotification('favorite_added');
            });
          } else {
            showNotification('favorite_exists');
          }
        });
      }
    });
  }

  function updateFavoritesList(favorites) {
    const favoritesList = document.getElementById('favorites-list');
    favoritesList.innerHTML = '';
    if (favorites.length > 0) {
      favorites.forEach((fav, index) => {
        const favItem = document.createElement('div');
        favItem.className = 'bookmark-item';
        favItem.innerHTML = `
          <span class="bookmark-title">${fav.title}</span>
          <div class="bookmark-actions">
            <button style="background-color: #2196F3; color: white;">${translations['open']?.message || chrome.i18n.getMessage('open') || 'Open'}</button>
            <button style="background-color: #f44336; color: white;">${translations['delete']?.message || chrome.i18n.getMessage('delete') || 'Delete'}</button>
          </div>
        `;
        favoritesList.appendChild(favItem);
        favItem.querySelector('button:nth-child(1)').addEventListener('click', () => {
          chrome.tabs.create({ url: `https://www.youtube.com/watch?v=${fav.id}` });
        });
        favItem.querySelector('button:nth-child(2)').addEventListener('click', () => {
          favorites.splice(index, 1);
          chrome.storage.sync.set({favorites: favorites}, () => favItem.remove());
        });
      });
    } else {
      const noFavoritesMessage = translations['no_favorites']?.message || chrome.i18n.getMessage('no_favorites') || 'no_favorites';
      favoritesList.innerHTML = `<div style="color: #666; text-align: center; padding: 10px;">${noFavoritesMessage}</div>`;
    }
  }

  function saveSettings() {
    const newSettings = {
      playbackSpeed: parseFloat(document.getElementById('custom-speed').value),
      focusMode: document.getElementById('focus-mode').checked,
      hideComments: document.getElementById('hide-comments').checked,
      autoHD: document.getElementById('auto-hd').checked,
      muteAndSpeedupAds: document.getElementById('mute-and-speedup-ads').checked,
      darkMode: document.getElementById('dark-mode').checked
    };
    chrome.storage.sync.set(newSettings, () => {
      showNotification('settings_saved');
      chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
        if (tabs[0] && tabs[0].url.includes('youtube.com/watch')) {
          chrome.tabs.sendMessage(tabs[0].id, { action: 'applySettings' }, function(response) {
            if (chrome.runtime.lastError) {
              console.warn('Failed to apply settings:', chrome.runtime.lastError.message);
            }
          });
        }
      });
    });
  }

  function resetSettings() {
    const defaultSettings = {
      playbackSpeed: 1.0,
      focusMode: false,
      hideComments: false,
      autoHD: true,
      muteAndSpeedupAds: true,
      darkMode: false,
      favorites: []
    };
    document.getElementById('custom-speed').value = defaultSettings.playbackSpeed;
    speedButtons.forEach(btn => btn.classList.remove('active'));
    document.querySelector('#speed-1\\.0').classList.add('active');
    document.getElementById('focus-mode').checked = defaultSettings.focusMode;
    document.getElementById('hide-comments').checked = defaultSettings.hideComments;
    document.getElementById('auto-hd').checked = defaultSettings.autoHD;
    document.getElementById('mute-and-speedup-ads').checked = defaultSettings.muteAndSpeedupAds;
    document.getElementById('dark-mode').checked = defaultSettings.darkMode;
    chrome.storage.sync.set(defaultSettings, () => {
      chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (tabs[0] && tabs[0].url.includes('youtube.com/watch')) {
          chrome.tabs.sendMessage(tabs[0].id, {action: 'applySettings'}, function(response) {
            if (chrome.runtime.lastError) {
              console.warn('Failed to reset settings:', chrome.runtime.lastError.message);
            }
            updateFavoritesList([]);
          });
        }
      });
    });
  }
});